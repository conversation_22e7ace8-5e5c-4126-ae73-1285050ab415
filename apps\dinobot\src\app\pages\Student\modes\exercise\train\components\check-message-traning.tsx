import { Message, UIMessage } from 'ai'
import { nanoid } from 'nanoid'
import Cookies from 'js-cookie'
import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { selectUseAvatarStore, useStreamingStore } from '@dinobot/stores'
import { Session } from '@dinobot/utils'

type CheckMessageTramingProps = {
    messages: UIMessage[]
    session?: Session
    setMessages: (
        messages: Message[] | ((messages: Message[]) => Message[])
    ) => void
}

const CheckMessageTraning = ({
    messages,
    setMessages,
    session
}: CheckMessageTramingProps) => {
    const {t} = useTranslation(['app/mode'],{keyPrefix:'train'})
    const { setIsStreaming } = useStreamingStore()
    const setKnowledgeBaseModeContent =
        selectUseAvatarStore.use.setKnowledgeBaseModeContent()

    useEffect(() => {
        const messagesLength = messages?.length
        setKnowledgeBaseModeContent(
            `voicie le chat : ${JSON.stringify(messages)}`
        )
        if (messagesLength === 4 && !session?.user) {
            Cookies.set('restrictFreeUser', 'yes')
            setIsStreaming(false)
            setMessages(currentMessages => [
                ...currentMessages,
                {
                    id: nanoid(),
                    role: 'assistant',
                    content: t('continue')
                }
            ])
        }
    }, [messages])

    return <></>
}

export default CheckMessageTraning
