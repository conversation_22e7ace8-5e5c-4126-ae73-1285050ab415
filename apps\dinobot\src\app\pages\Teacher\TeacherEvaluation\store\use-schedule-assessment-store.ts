import { createSelectors } from '@dinobot/stores'
import { ControlPartialWithRelations } from '@dinobot/prisma'
import { create } from 'zustand'
import type {
    ScheduleAssessmentStoreType,
    ScheduleAssessmentStoreState,
    ScheduleAssessmentStoreAction
} from '../teacherEvaluation.types'
const ScheduleAssessmentStoreInitialState: ScheduleAssessmentStoreState = {
    controls: [],
    selectedControl: undefined,
    level: ''
}

export const useScheduleAssessmentStore = create<ScheduleAssessmentStoreType>(
    set => ({
        ...ScheduleAssessmentStoreInitialState,
        setControls: controls => set({ controls }),
        setSelectedControl: controle => set({ selectedControl: controle }),
        setLevel: level => set({ level })
    })
)
export const selectUseScheduleAssessmentStore = createSelectors(
    useScheduleAssessmentStore
)
