# Multi-stage build for production deployment - DEBUG VERSION
FROM node:22-alpine AS base

# Install pnpm
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy only package.json files first for better dependency caching
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY nx.json ./
COPY tsconfig.base.json ./
COPY tsconfig.json ./

# Copy only package.json files from apps and libs (excluding e2e)
COPY apps/dinobot/package.json ./apps/dinobot/
COPY apps/dinobot-backend/package.json ./apps/dinobot-backend/
COPY libs/components-ui/package.json ./libs/components-ui/
COPY libs/hooks/package.json ./libs/hooks/
COPY libs/prisma/package.json ./libs/prisma/
COPY libs/stores/package.json ./libs/stores/
COPY libs/utils/package.json ./libs/utils/

# Install dependencies (this layer will be cached if package.json files don't change)
RUN pnpm install --no-frozen-lockfile

# Copy source code after dependencies are installed
COPY apps/ ./apps/
COPY libs/ ./libs/

# Build stage - DEBUG VERSION (stops here for interactive testing)
FROM base AS debug

# Sync Nx workspace 
RUN pnpm nx sync

# Install additional debugging tools
RUN apk add --no-cache bash

# Set default command to bash for interactive debugging
CMD ["/bin/bash"]