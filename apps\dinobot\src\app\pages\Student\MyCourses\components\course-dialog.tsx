import React from 'react'
import { toast } from 'sonner'
import { FieldValues, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import useMyCourses from '../hooks/useMyCourses'
import { Button, Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger, Input } from '@dinobot/components-ui'

type CourseDialogProps = {
    trigger: React.ReactNode
}

function CourseDialog({ trigger }: CourseDialogProps) {
    const { t } = useTranslation(['app/courses/index'])

    const { dialogOpen, setDialogOpen, joinClass, isJoining, error, setError } = useMyCourses()

    const {
        register,
        reset,
        handleSubmit,
        formState: { errors }
    } = useForm<any>()

    const onSubmit = async (data: FieldValues) => {
        joinClass(
            { code: data.code as string },
            {
                onSuccess: () => {
                    toast.success(t('success.text'))
                    reset()
                },
                onError: (error: any) => {
                    const errorMessage = error.response?.data?.message || error.message
                    toast.error(errorMessage)
                }
            }
        )
    }

    const onClose = () => {
        setDialogOpen(false)
        setError(null)
        reset()
    }

    return (
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>{trigger}</DialogTrigger>
            <DialogContent className="h-fit">
                <DialogHeader>
                    <DialogTitle className="flex justify-start p-2 text-dinoBotGray font-bold">
                        {t('dialog.title')}
                    </DialogTitle>
                    <DialogDescription className="flex justify-center text-dinoBotGray">
                        {t('dialog.description')}
                    </DialogDescription>
                </DialogHeader>
                <form
                    className="flex flex-col gap-2"
                    onSubmit={handleSubmit(onSubmit)}
                >
                    <Input
                        onKeyDown={e => {
                            if (e.key === 'Enter') {
                                e.preventDefault()
                                handleSubmit(onSubmit)()
                            }
                        }}
                        placeholder="XXX-XXXX-XXX"
                        disabled={isJoining}
                        {...register('code', {
                            minLength: {
                                value: 10,
                                message: t('error.length')
                            },
                            required: {
                                value: true,
                                message: t('error.required')
                            }
                        })}
                    />
                    {errors.code?.message && (
                        <span className="text-red-500">
                            {errors.code.message.toString()}
                        </span>
                    )}
                    {error && (
                        <span className="text-red-500">
                            {error}
                        </span>
                    )}
                    <DialogFooter
                        className="w-full h-fit flex mt-12"
                        style={{ justifyContent: 'space-between' }}
                    >
                        <Button
                            type="button"
                            variant="link"
                            className="text-dinoBotLightGray underline"
                            onClick={onClose}
                            disabled={isJoining}
                        >
                            {t('dialog.cancel')}
                        </Button>
                        <Button
                            variant="link"
                            className="text-dinoBotBlue underline"
                            type="submit"
                            disabled={isJoining}
                        >
                            {t('dialog.submit')}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}

export default CourseDialog
