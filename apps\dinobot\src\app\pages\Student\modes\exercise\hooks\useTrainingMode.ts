import { useMutation, useQuery } from '@tanstack/react-query';
import { useAuthApiClient } from '../../../../../contexts/AppContext';
import { TrainingModeGeneratorInput, Question } from '../trainig.types';

export function useTrainingMode() {
  const authApiClient = useAuthApiClient();

  // Get random questions by part
  const useRandomQuestionsByPart = (partId?: string, numberOfQuestions?: number) => {
    return useQuery({
      queryKey: ['random-questions-part', partId, numberOfQuestions],
      queryFn: async (): Promise<Question[]> => {
        const params = new URLSearchParams();
        if (partId) params.append('partId', partId);
        if (numberOfQuestions) params.append('numberOfQuestions', numberOfQuestions.toString());
        
        const response = await authApiClient.get(`/api/training-mode/questions/random?${params.toString()}`);
        return response.data;
      },
      enabled: !!partId && !!numberOfQuestions,
      staleTime: 2 * 60 * 1000,
    });
  };

  // Get random questions by chapter
  const useRandomQuestionsByChapter = (chapterId?: string, numberOfQuestions?: number) => {
    return useQuery({
      queryKey: ['random-questions-chapter', chapterId, numberOfQuestions],
      queryFn: async (): Promise<Question[]> => {
        const params = new URLSearchParams();
        if (chapterId) params.append('chapterId', chapterId);
        if (numberOfQuestions) params.append('numberOfQuestions', numberOfQuestions.toString());
        
        const response = await authApiClient.get(`/api/training-mode/questions/random?${params.toString()}`);
        return response.data;
      },
      enabled: !!chapterId && !!numberOfQuestions,
      staleTime: 2 * 60 * 1000,
    });
  };

  // Get questions by part
  const useQuestionsByPart = (partId?: string) => {
    return useQuery({
      queryKey: ['questions-part', partId],
      queryFn: async (): Promise<Question[]> => {
        const response = await authApiClient.get(`/api/training-mode/questions/part/${partId}`);
        return response.data;
      },
      enabled: !!partId,
      staleTime: 5 * 60 * 1000,
    });
  };

  // Get question knowledge by part
  const useQuestionKnowledgeByPart = (partId?: string) => {
    return useQuery({
      queryKey: ['question-knowledge', partId],
      queryFn: async (): Promise<string[]> => {
        const response = await authApiClient.get(`/api/training-mode/questions/knowledge/${partId}`);
        return response.data;
      },
      enabled: !!partId,
      staleTime: 5 * 60 * 1000,
    });
  };

  // Get all parts of chapter as string
  const useAllPartsOfChapter = (chapterId?: string) => {
    return useQuery({
      queryKey: ['all-parts-chapter', chapterId],
      queryFn: async (): Promise<{ parts: string }> => {
        const response = await authApiClient.get(`/api/training-mode/parts/${chapterId}/all`);
        return response.data;
      },
      enabled: !!chapterId,
      staleTime: 5 * 60 * 1000,
    });
  };

  // Get chapter from part ID
  const useChapterFromPart = (partId?: string) => {
    return useQuery({
      queryKey: ['chapter-from-part', partId],
      queryFn: async () => {
        const response = await authApiClient.get(`/api/training-mode/parts/${partId}/chapter`);
        return response.data;
      },
      enabled: !!partId,
      staleTime: 5 * 60 * 1000,
    });
  };

  // Get chapter from multiple part IDs
  const getChapterFromPartIds = useMutation({
    mutationFn: async (partIds: string[]) => {
      const response = await authApiClient.post('/api/training-mode/chapters/from-parts', {
        partIds
      });
      return response.data;
    },
  });

  // Generate exercise from file
  const generateExerciseFromFile = useMutation({
    mutationFn: async (input: TrainingModeGeneratorInput) => {
      const response = await authApiClient.post('/api/training-mode/exercises/generate', input);
      return response.data;
    },
  });

  return {
    // Query hooks
    useRandomQuestionsByPart,
    useRandomQuestionsByChapter,
    useQuestionsByPart,
    useQuestionKnowledgeByPart,
    useAllPartsOfChapter,
    useChapterFromPart,

    // Mutations
    getChapterFromPartIds,
    generateExerciseFromFile,

    // Loading states
    isGettingChapterFromParts: getChapterFromPartIds.isPending,
    isGeneratingFromFile: generateExerciseFromFile.isPending,

    // Error states
    getChapterFromPartsError: getChapterFromPartIds.error,
    generateFromFileError: generateExerciseFromFile.error,
  };
}