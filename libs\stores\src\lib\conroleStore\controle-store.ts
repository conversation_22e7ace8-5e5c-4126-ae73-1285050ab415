import { create } from 'zustand'
import { z } from 'zod'
import { ControlPartialWithRelationsSchema } from '@dinobot/prisma'
import { ControleModeGeneratorOutput, ControlFeedbackOutput, TrainingModeGeneratorOutput } from '@dinobot/utils'
import { createSelectors } from '@dinobot/stores'

export interface CtrlInfo {
  chapterId: string;
  partId: string;
}

interface CtrlState {
  level: string | null;
  subject: string | null;
  ctrlInfo: CtrlInfo;
  currentGCtrl: TrainingModeGeneratorOutput[];
  controle: ControlFeedbackOutput | null;
  isRunning: boolean;
  isLoading: boolean;
  isNoLimit: boolean;
  timeHidden: boolean;
  time: Date;
  startTime: () => void;
  pauseTime: () => void;
  hasSubmission: boolean;
}

interface CtrlStateActions {
  setLevel: (level: string | null) => void;
  setSubject: (level: string | null) => void;
  setControle: (controle: ControlFeedbackOutput | null) => void;
  updateCtrlInfo: (field: keyof CtrlInfo, value: string | number) => void;
  transformControleData: (
    data: ControleModeGeneratorOutput[],
    t: any,
  ) => ControlFeedbackOutput | null;
  transformControleDataWithoutSubmission: (
    data: z.infer<typeof ControlPartialWithRelationsSchema>,
    t: any,
  ) => ControlFeedbackOutput | null;
  letstart: (start: () => void, isRunning: boolean, pause: () => void) => void;
  setIsRunning: (isRunning: boolean) => void;
  setIsNoLimit: (isNoLimit: boolean) => void;
  timeStart: () => void;
  setCurrentGCtrl: (input: TrainingModeGeneratorOutput[]) => void;
  setLoading: (loading: boolean) => void;
  setTimeHidden: (timeHidden: boolean) => void;
  setHasSubmission: (hasSubmission: boolean) => void;
  setTime: (time: Date) => void;
  reset: () => void;
}

const initialCtrlState: CtrlState = {
  level: null,
  subject: null,
  ctrlInfo: {
    chapterId: '',
    partId: '',
  },
  time: new Date(new Date().setHours(0, 0, 0, 0)),
  currentGCtrl: [],
  controle: null,
  isRunning: false,
  isLoading: true,
  isNoLimit: false,
  timeHidden: true,
  startTime: () => {
    // Will be set by letstart method
  },
  pauseTime: () => {
    // Will be set by letstart method
  },
  hasSubmission: true,
};

export const useCtrlModeStore = create<CtrlState & CtrlStateActions>()((set, get) => ({
  ...initialCtrlState,
  setLevel(level) {
    set((state) => ({ ...state, level }));
  },
  setSubject(subject) {
    set((state) => ({ ...state, subject }));
  },
  updateCtrlInfo(field, value) {
    set((state) => ({
      ...state,
      ctrlInfo: { ...state.ctrlInfo, [field]: value },
    }));
  },
  setCurrentGCtrl(input) {
    set((state) => ({ ...state, currentGCtrl: input }));
  },
  setControle(controle: ControlFeedbackOutput | null) {
    set((state) => ({ ...state, controle: controle }));
  },
  transformControleData(data: ControleModeGeneratorOutput[], t) {
    if (!data) {
      return null;
    }
    const transformedData: ControlFeedbackOutput = {
      score: 0,
      scoreExplanation: '',
      exercises: data.map((item, i) => ({
        id: i.toString(),
        title: t('exo') + ' ' + (i + 1),
        score: 0,
        questions: item.map((q, i) => ({
          id: i.toString(),
          questionContent: q.questionContent,
          answer: undefined,
          feedback: '',
          desmosCode: q.desmosCode,
          contentType: q.contentType,
          medias: [],
        })),
        medias: [],
        statement: '',
        hasStatment: false,
      })),
    };
    return transformedData;
  },
  transformControleDataWithoutSubmission(
    data: z.infer<typeof ControlPartialWithRelationsSchema>,
    t: (key: string) => string,
  ): ControlFeedbackOutput | null {
    if (!data || !Array.isArray(data.exercises)) {
      return null;
    }

    const transformedData: any = {
      score: 0,
      scoreExplanation: '',
      exercises: data.exercises.map((item, i) => ({
        id: i,
        title: `${t('exo')} ${i + 1}`,
        score: 0,
        medias: item.medias,
        hasStatment: item.hasStatment,
        statement: item.statement,
        questions: Array.isArray(item.questions)
          ? item.questions.map((q, j) => ({
              id: q.id,
              questionContent: q.content ?? '',
              answer: undefined,
              feedback: '',
              desmosCode: q.desmosCode ?? '',
              contentType: q.type ?? '',
              medias: q.medias ?? [],
            }))
          : [],
      })),
    };

    return transformedData;
  },
  letstart(start, isRunning, pause) {
    set((state) => ({
      ...state,
      startTime: start,
      isRunning,
      pauseTime: pause,
    }));
  },
  setHasSubmission(hasSubmission) {
    set((state) => ({ ...state, hasSubmission }));
  },
  setTimeHidden(timeHidden) {
    set((state) => ({ ...state, timeHidden }));
  },
  setIsRunning(isRunning) {
    set((state) => ({ ...state, isRunning }));
  },
  setLoading(loading) {
    set((state) => ({ ...state, isLoading: loading }));
  },
  setIsNoLimit(isNoLimit) {
    set((state) => ({ ...state, isNoLimit }));
  },
  timeStart() {
    get().startTime();
  },
  setTime: (time) => set((state) => ({ ...state, time })),
  reset() {
    set(initialCtrlState);
  },
}));

export const selectUseCtrlModeStore = createSelectors(useCtrlModeStore);

