// import {Document} from "react-pdf"

import React from 'react'
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer'

// Define styles
const styles = StyleSheet.create({
    page: {
        flexDirection: 'column',
        backgroundColor: '#ffffff'
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 10,
        backgroundColor: 'rgba(0, 123, 255, 0.05)' // Approximating dinoBotVibrantBlue/5
    },
    headerText: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#007bff' // Approximating dinoBotBlue
    },
    content: {
        margin: 10,
        padding: 10
    },
    sectionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        borderBottomWidth: 1,
        borderBottomColor: '#007bff',
        marginBottom: 10,
        paddingBottom: 5
    },
    sectionHeaderText: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#007bff'
    },
    list: {
        marginLeft: 15
    },
    listItem: {
        marginBottom: 5
    }
})

// PDF Document component
const ExoPrintViewPDF = ({ exo }: { exo: { questionContent: string }[] }) => (
    <Document>
        <Page size="A4" style={styles.page}>
            <View style={styles.header}>
                {/* Uncomment and adjust if you want to include an image
        <Image src="./dinobot-logo-chat.svg" style={{ width: 48, height: 48 }} />
        */}
                <Text style={styles.headerText}>DinoBot</Text>
            </View>
            <View style={styles.content}>
                <View style={styles.sectionHeader}>
                    <Text style={styles.sectionHeaderText}>Exercice</Text>
                </View>
                <View style={styles.list}>
                    {exo &&
                        exo.length > 0 &&
                        exo.map((x, index) => (
                            <View key={index} style={styles.listItem}>
                                <Text>• {x.questionContent}</Text>
                            </View>
                        ))}
                </View>
            </View>
        </Page>
    </Document>
)

export default ExoPrintViewPDF
