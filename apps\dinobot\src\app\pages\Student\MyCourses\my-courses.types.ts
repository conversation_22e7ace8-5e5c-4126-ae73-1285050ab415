import { ClassWithPartialRelations } from "@dinobot/prisma/lib/generated/zod/modelSchema/ClassSchema"

export interface MyCoursesState {
  courses: ClassWithPartialRelations[]
  isLoading: boolean
  error: string | null
  dialogOpen: boolean
}

export interface MyCoursesActions {
  setCourses: (courses: ClassWithPartialRelations[]) => void
  addCourse: (course: ClassWithPartialRelations) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setDialogOpen: (open: boolean) => void
  reset: () => void
}

export interface JoinClassData {
  code: string
}

export interface FeatureFlag {
  route?: string
  enabled: boolean
}

export type MyCoursesStore = MyCoursesState & MyCoursesActions
