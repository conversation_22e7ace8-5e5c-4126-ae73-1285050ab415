import React from 'react'
import moment from 'moment'
import { TimeSelector } from './time-selector'
import { Label } from '@dinobot/components-ui'
import { IconInfo } from '@dinobot/components-ui'
import { Input } from '@dinobot/components-ui'
import { Control } from 'react-hook-form'
import {
    FormControl,
    FormField,
    FormItem,
    FormMessage
} from '@dinobot/components-ui'
import { useTranslation } from 'react-i18next'

type DateEvalProps = {
    control: Control<any>
}

const DateEval = ({ control }: DateEvalProps) => {
    const { t } = useTranslation('teacher/myClass/evaluation/schedule/date')
    const today = moment().format('YYYY-MM-DD')

    // Set default time to current time + 1 minute
    const now = moment().add(60, 'seconds')
    const defaultStartTime = {
        hours: now.format('HH'),
        minutes: now.format('mm')
    }

    return (
        <div className="flex flex-col gap-2 w-fit ">
            <h4 className="flex gap-2 items-center">
                {t('title')}
                <IconInfo fill="#5c5c5c" />
            </h4>
            <div className="border rounded-sm p-4 flex flex-col gap-2">
                {/* Date de début */}
                <div className="flex flex-col justify-center gap-2">
                    <Label htmlFor="datevent-start">{t('dispo')}</Label>
                    <div className="flex gap-2 items-center">
                        <FormField
                            name="availableDate"
                            control={control}
                            rules={{
                                required: {
                                    value: true,
                                    message: t('dispo_error')
                                }
                            }}
                            defaultValue={today}
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <Input
                                            type="date"
                                            id="datevent-start"
                                            value={field.value}
                                            onChange={field.onChange}
                                            placeholder={t('dispo_placeholder')}
                                            className="w-fit"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            name="startTime"
                            control={control}
                            defaultValue={defaultStartTime}
                            render={({ field }) => (
                                <TimeSelector
                                    value={field.value}
                                    onChange={field.onChange}
                                />
                            )}
                        />
                    </div>
                </div>

                {/* Date de fin */}
                <div className="flex flex-col justify-center gap-2">
                    <Label htmlFor="datevent-end">{t('due')}</Label>
                    <div className="flex gap-2 items-center">
                        <FormField
                            name="dueDate"
                            control={control}
                            rules={{
                                required: {
                                    value: true,
                                    message: t('due_error')
                                }
                            }}
                            defaultValue={today}
                            render={({ field }) => (
                                <FormItem>
                                    <FormControl>
                                        <Input
                                            type="date"
                                            id="datevent-end"
                                            value={field.value}
                                            onChange={field.onChange}
                                            placeholder={t('due_placeholder')}
                                            className="w-fit"
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            name="endTime"
                            control={control}
                            defaultValue={{ hours: '23', minutes: '59' }}
                            render={({ field }) => (
                                <TimeSelector
                                    value={field.value}
                                    onChange={field.onChange}
                                />
                            )}
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default DateEval
