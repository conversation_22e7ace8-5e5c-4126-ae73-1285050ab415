import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import type { SearchSimilarQuestionsParams } from '../teacherEvaluation.types'

export const useSearchSimilarQuestions = (params: SearchSimilarQuestionsParams) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['search-similar-questions', params],
        queryFn: async () => {
            if (!apiClient || !params.partId) return []
            
            const response = await apiClient.post('/api/training-mode/questions/search', params)
            return response.data
        },
        enabled: !!apiClient && !!params.partId,
        gcTime: 15000
    })
}