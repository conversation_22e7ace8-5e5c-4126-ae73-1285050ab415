import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { ControlPartialWithRelations, Domain, Level } from '@dinobot/prisma'

export const useEvaluationData = (classId?: string) => {
    const apiClient = useAuthApiClient()
    const queryClient = useQueryClient()

    // Fetch evaluations for a class
    const {
        data: evaluations,
        isLoading: isLoadingEvaluations,
        error: evaluationsError
    } = useQuery({
        queryKey: ['evaluations', classId],
        queryFn: async (): Promise<ControlPartialWithRelations[]> => {
            if (!classId) return []
            const response = await apiClient.get(`/api/evaluation-scheduler/class/${classId}/controls`)
            return response.data
        },
        enabled: !!classId && !!apiClient
    })

    // Fetch domains
    const {
        data: domains,
        isLoading: isLoadingDomains,
        error: domainsError
    } = useQuery({
        queryKey: ['domains'],
        queryFn: async (): Promise<Domain[]> => {
            const response = await apiClient.get('/api/domains')
            return response.data
        },
        enabled: !!apiClient
    })

    // Fetch levels
    const {
        data: levels,
        isLoading: isLoadingLevels,
        error: levelsError
    } = useQuery({
        queryKey: ['levels'],
        queryFn: async (): Promise<Level[]> => {
            const response = await apiClient.get('/api/levels')
            return response.data
        },
        enabled: !!apiClient
    })

    // Create evaluation mutation
    const createEvaluationMutation = useMutation({
        mutationFn: async (evaluationData: any) => {
            const response = await apiClient.post('/api/evaluation-scheduler/controls', evaluationData)
            return response.data
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['evaluations'] })
        }
    })

    // Update evaluation mutation
    const updateEvaluationMutation = useMutation({
        mutationFn: async ({ id, data }: { id: string; data: any }) => {
            const response = await apiClient.put(`/api/evaluation-scheduler/controls/${id}`, data)
            return response.data
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['evaluations'] })
        }
    })

    // Delete evaluation mutation
    const deleteEvaluationMutation = useMutation({
        mutationFn: async (id: string) => {
            const response = await apiClient.delete(`/api/evaluation-scheduler/controls/${id}`)
            return response.data
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['evaluations'] })
        }
    })

    const isLoading = isLoadingEvaluations || isLoadingDomains || isLoadingLevels
    const error = evaluationsError || domainsError || levelsError

    return {
        // Data
        evaluations: evaluations || [],
        domains: domains || [],
        levels: levels || [],

        // Loading states
        isLoading,
        isCreating: createEvaluationMutation.isPending,
        isUpdating: updateEvaluationMutation.isPending,
        isDeleting: deleteEvaluationMutation.isPending,

        // Error states
        error: error?.message,

        // Actions
        createEvaluation: createEvaluationMutation.mutate,
        createEvaluationAsync: createEvaluationMutation.mutateAsync,
        updateEvaluation: updateEvaluationMutation.mutate,
        updateEvaluationAsync: updateEvaluationMutation.mutateAsync,
        deleteEvaluation: deleteEvaluationMutation.mutate
    }
}

export default useEvaluationData
