//Hooks
import React, { useEffect, useMemo, useState } from 'react'
import Cookies from 'js-cookie'

import { toast } from 'sonner'
import MultipleSelect from 'react-select'


//API-Function-Call
import { useExerciseData } from '../../../hooks'

//Components
import ExoFromExamTableLayout from './exo-from-exam-table-layout'
import { getLangProps } from '@dinobot/utils'
import { Domain } from '@dinobot/prisma'
import useExoModeStore, { selectUseExoModeStore } from '@dinobot/stores/lib/exercise-mode-store/exercise-store'
import { useTranslation } from 'react-i18next'
import { useAccountStore } from '@dinobot/stores'
import { useNavigate,useLocation } from 'react-router-dom'
import { Button, Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue, Slider, Textarea,InfoTooltip,ErrorTooltip } from '@dinobot/components-ui'
import { ExoOutput, ExoSubject } from '../../../trainig.types'

interface ExoFromDbProps {
    domains: Domain[]
    // getSubjectsByDomainIdAndLevelId: (
    //     domainId: number,
    //     levelId: number | null
    // ) => Promise<ExoSubject[]>
}

function ExoFromExam({
    domains,
    // getSubjectsByDomainIdAndLevelId
}: ExoFromDbProps) {
    const [selectedSubject, setSelectedSubject] = useState<string[]>([])
    const [exams, setExams] = useState<ExoOutput[]>([])
    const [subjects, setSubjects] = useState<ExoSubject[]>([])
    const formData = selectUseExoModeStore.use.exoInfoFromExam()
    const handleFormChange = selectUseExoModeStore.use.updateExoInfoFromExam()
    const [showError, setShowError] = useState<boolean>(false)
    const setMode = useExoModeStore(state => state.setMode)
    const { t , i18n } = useTranslation(['app/mode'], { keyPrefix: 'exo.tab.exam' })
    const { t: t2 } = useTranslation(['app/chat'], { keyPrefix: 'exam-dialog' })
    const { t: t3 } = useTranslation(['app/mode'], { keyPrefix: 'exo.tab.db' })
    const navigate = useNavigate()
    const path = useLocation().pathname
    const topic = Cookies.get('topic')
    const topicId = Cookies.get('topicId')
    const { user } = useAccountStore()
    const lang = i18n.language

    // API hooks
    const { useSubjectsByDomainAndLevel, useExamsByDomainLevelSubjects } = useExerciseData()

    // Get subjects by domain and level
    const { data: subjectsData } = useSubjectsByDomainAndLevel(
        formData.domainId ? Number.parseInt(formData.domainId) : undefined,
        user?.userLevel?.id
    )

    // Get exams by domain, level and subjects
    const { data: examsData } = useExamsByDomainLevelSubjects(
        formData.domainId ? Number.parseInt(formData.domainId) : undefined,
        user?.userLevel?.id,
        selectedSubject.length > 0 ? selectedSubject : undefined
    )

    useEffect(() => {
        setMode('FROM_EXAM')
    }, [])

    // getSubjet - Implemented with React Query hook above
    useEffect(() => {
        setSelectedSubject([])
        setExams([])
        if (subjectsData) {
            setSubjects(subjectsData)
        } else {
            setSubjects([])
        }
    }, [formData.domainId, user?.userLevel?.id, subjectsData])

    // Update exams when examsData changes
    useEffect(() => {
        if (examsData) {
            setExams(examsData)
        }
    }, [examsData])

    const changeSubjects = async () => {
        // getExamsByDomainIdAndLevelIdAndSubjects - Now implemented with React Query hook
        // The exams will be automatically updated via the useExamsByDomainLevelSubjects hook
        // when selectedSubject changes, which triggers the API call with the new subjects
    }

    const filteredSubjectId = useMemo(() => {
        return subjects
            .filter(subject => selectedSubject.includes(subject.name))
            .map(subject => subject.id)
    }, [selectedSubject, subjects])

    useEffect(() => {
        if (selectedSubject.length > 0 && formData.domainId) {
            changeSubjects()
        } else {
            setExams([])
        }
    }, [selectedSubject, formData.domainId])

    const handleQuestionNumberChange = (value: number) => {
        try {
            // const int = parseInt(value)
            if (value > 10) {
                handleFormChange('qstNbr', 10)
                toast.info(t('n10'), { duration: 2500 })
            } else if (value < 1) {
                handleFormChange('qstNbr', 1)
                toast.info(t('n1'), { duration: 2500 })
            } else {
                handleFormChange('qstNbr', value)
            }
        } catch (error) {
            console.log(error)
            handleFormChange('qstNbr', 5)
        }
    }

    useEffect(() => {
        if (formData.qstNbr) handleQuestionNumberChange(formData.qstNbr)
    }, [formData.qstNbr])

    const submit = () => {
        if (formData.examId) {
            if (formData.qstNbr) {
                setShowError(false)
                setMode('FROM_EXAM')
                navigate(`/${path}/train`)
            } else {
                toast.error(t('n1&10'))
            }
        } else {
            setShowError(true)
            toast.info(t('n10'))
        }
    }

    return (
        <div className="flex flex-col gap-9 w-full">
            <div className="flex flex-row gap-2 w-full">
                <div className="w-24 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                    {t3('subject.name')}{' '}
                </div>
                <Select
                    value={formData.domainId}
                    onValueChange={value => handleFormChange('domainId', value)}
                >
                    <SelectTrigger className="max-w-full">
                        <SelectValue placeholder={t3('subject.placeholder')} />
                    </SelectTrigger>
                    <SelectContent
                        className={`${domains.length > 5 ? 'h-48' : 'h-fit'}`}
                    >
                        <SelectGroup>
                            {domains.map(domain => (
                                <SelectItem
                                    key={domain.id}
                                    value={domain.id.toString()}
                                >
                                    {getLangProps({
                                        obj: domain,
                                        base: 'name',
                                        lang
                                    })}
                                </SelectItem>
                            ))}
                        </SelectGroup>
                    </SelectContent>
                </Select>
            </div>
            <div className="w-full flex flex-row gap-2">
                <div className="w-24 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                    {t2('categorie')}{' '}
                </div>
                <MultipleSelect
                    isMulti
                    className="w-full"
                    options={subjects.map(sub => ({
                        label: sub.name,
                        value: sub.name
                    }))}
                    placeholder={t('chois')}
                    onChange={value => {
                        setSelectedSubject(value.map(v => v.value))
                    }}
                    value={selectedSubject.map(v => ({
                        label: v,
                        value: v
                    }))}
                    isDisabled={!formData.domainId}
                />
            </div>

            {exams.length > 0 && (
                <>
                    <div className="flex flex-col gap-1">
                        <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                            {t('exam')}{' '}
                            {(formData.examId.length <= 0 && !showError) ||
                            formData.examId.length > 0 ? (
                                <InfoTooltip message={t('chois-savoir')} />
                            ) : (
                                <ErrorTooltip
                                    message={t('error-chois-savoir')}
                                />
                            )}
                        </div>
                        <div>
                            <p className="text-[10px] text-[#707070]">
                                {t('chois-savoir-text')}
                            </p>
                            <p className="text-[10px] font-light text-[#707070]">
                                {exams.length} {t('result')}
                            </p>
                        </div>
                        <div className="mt-2">
                            <ExoFromExamTableLayout exams={exams} />
                        </div>
                    </div>
                    <div className="w-full flex flex-col md:flex-row gap-4 md:gap-12">
                        {/* <div className='w-full sm:w-2/5'>
              <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                Nombre de questions
              </div>
              <Input type="number" value={formData.qstNbr} min={1} max={10} className='h-9 rounded-xl' onChange={(e)=>handleFormChange("qstNbr",e.target.value)}/>
            </div> */}
                        <div className="w-full sm:w-2/5">
                            <div className="flex gap-1 items-center justify-between text-sm font-bold text-dinoBotDarkGray">
                                {t('difficulty')}{' '}
                                <span>{formData.difficulty}/3</span>
                            </div>
                            <div className="py-3">
                                <Slider
                                    defaultValue={[formData.difficulty]}
                                    min={0}
                                    max={3}
                                    step={1}
                                    className={'w-full '}
                                    onValueChange={value =>
                                        handleFormChange('difficulty', value[0])
                                    }
                                />
                            </div>
                        </div>
                    </div>
                    <p className="text-[10px] text-[#707070]">{t('have')}</p>
                </>
            )}

            <div className="flex flex-col gap-1">
                <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                    {' '}
                    {t('prompt')}{' '}
                    <span className="font-normal">{t('opt')}</span>{' '}
                </div>
                <div className="mt-2">
                    <Textarea
                        placeholder={t('rid')}
                        value={formData.customPrompt}
                        onChange={e => {
                            if (e.target.value.length <= 1000)
                                handleFormChange('customPrompt', e.target.value)
                        }}
                    ></Textarea>
                    <p className="text-xs text-right mt-1 text-dinoBotGray">
                        {formData.customPrompt.length ?? 0}/1000{' '}
                        {t('caracteres')}
                    </p>
                </div>
            </div>
            <div className="w-full flex justify-center items-center mt-2">
                <Button
                    className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
                    onClick={submit}
                >
                    {t('submit')}
                </Button>
            </div>
        </div>
    )
}

export default ExoFromExam
