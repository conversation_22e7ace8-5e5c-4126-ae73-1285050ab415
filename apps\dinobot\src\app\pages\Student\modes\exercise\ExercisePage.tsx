import { useEffect } from 'react'
import ExoModeLayout from './components/exo-mode-layout'
import { useFeatureRoutes } from '../../../../hooks/useFeatureFlags'
import { useNavigate } from 'react-router-dom'

export default function ExerciseModePage() {
    const navigate = useNavigate()
    const { data: featureFlags } = useFeatureRoutes('STUDENT_EXERCISE_MODE')

    useEffect(() => {
        if (featureFlags && Array.isArray(featureFlags) && featureFlags[0] != null) {
            navigate(`/${featureFlags[0]}`)
        }
    }, [featureFlags, navigate])

    return (
        <div className="size-full">
            <ExoModeLayout />
        </div>
    )
}
