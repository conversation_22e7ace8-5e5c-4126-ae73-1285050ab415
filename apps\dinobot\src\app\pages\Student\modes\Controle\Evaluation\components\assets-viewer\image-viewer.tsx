import ImageNotFound from './image-not-found'

interface Props {
    url: string
    alt: string
}

export default function ImageViewer({ url, alt }: Props) {
    return (
        <div className="w-full h-[150px] flex items-center bg-dinoBotLightGray/30 rounded">
            {url ? (
                <img width="300" height="280" src={url} alt={alt} />
            ) : (
                <ImageNotFound />
            )}
        </div>
    )
}
