import { useEffect } from 'react';
import Chat from './components/Chat';
import Cookies from 'js-cookie'
import { Session } from '@dinobot/utils';
import { useFeatureRoutes } from '../../hooks/useFeatureFlags'
import { useNavigate } from 'react-router-dom'


const ExamByIdPage = () => {
    const session = JSON.parse(Cookies.get('session') || '{}') as Session
    const navigate = useNavigate()
    const { data: featureFlags } = useFeatureRoutes('STUDENT_EXAM_MODE')

    useEffect(() => {
        if (featureFlags && Array.isArray(featureFlags) && featureFlags[0] != null) {
            navigate(`/${featureFlags[0]}`)
        }
    }, [featureFlags, navigate])

    useEffect(() => {
        if (session?.user?.type === 'teacher') {
            navigate('/created-controls')
        }
    }, [session, navigate])

    return (
        <Chat session={session}/>
    );
}

export default ExamByIdPage