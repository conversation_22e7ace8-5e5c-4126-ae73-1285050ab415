import React, { useEffect, useRef, useState } from 'react';
import moment from 'moment';
import { useNavigate, useRouteLoaderData } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { getLangDir } from 'rtl-detect';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useAuthApiClient } from '../../../../../contexts/AppContext';
import { toast } from 'sonner';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@dinobot/components-ui';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@dinobot/components-ui';
import { Button } from '@dinobot/components-ui';
import { Check, Clipboard, Printer } from 'lucide-react';
import { useReactToPrint } from 'react-to-print';
import { cn } from '@dinobot/utils';
import Loading from './loading';
import { selectUseLocalStorageStore, useCtrlModeStore } from '@dinobot/stores';
import { Domain, User } from '@dinobot/prisma';
import { getLangProps } from '@dinobot/utils';
import { useGenerateControle, useDomain } from '../hooks/useControle';
import { MemoizedReactMarkdown } from '@dinobot/components-ui';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeRaw from 'rehype-raw';
import rehypemathjax from 'rehype-mathjax';
import { DesmosUi } from '@dinobot/components-ui';

// This is the left side of the control mode

const ControlPreview = () => {
  const { user } = useRouteLoaderData('user') as { user: User };
  const locale = 'fr';

  const { t, i18n } = useTranslation(['app/mode/controle/controlPreview']);
  const tt = useTranslation(['app/mode/train']).t;
  const dir = getLangDir(i18n.language);
  const apiClient = useAuthApiClient();
  const navigate = useNavigate();
  const {
    subject,
    ctrlInfo,
    setControle,
    transformControleData,
    controle,
    timeStart,
    isRunning,
    isLoading,
    setLoading,
    reset,
  } = useCtrlModeStore();
  const domainId = selectUseLocalStorageStore.use.domainId();
  const [domain, setDomain] = useState<Domain>({
    id: 0,
    name: '',
    name_en: null,
    name_ar: null,
    color: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    levelTypeId: 0,
    disabled: false,
  });
  const ctrlRef = useRef<HTMLDivElement>(null);

  const { data: domainData } = useDomain(domainId);

  const generateControleMutation = useGenerateControle();

  const getControlQuestions = async () => {
    console.log('getControlQuestions', subject, ctrlInfo.chapterId);
    if (domainData) {
      setDomain(domainData);
    }
    if (!ctrlInfo.chapterId || !subject) {
      reset();
      navigate('/create-controle');
      return;
    }

    setLoading(true);
    generateControleMutation.mutate(
      {
        chapterId: ctrlInfo.chapterId,
        domain: subject!,
      },
      {
        onSuccess: (result) => {
          if (result && result.length > 0) {
            setControle(transformControleData(result, tt));
            timeStart();
          } else {
            toast.error(t('noControle'));
            reset();
            navigate('/create-controle');
          }
          setLoading(false);
        },
        onError: () => {
          toast.error(t('error'));
          reset();
          navigate('/create-controle');
          setLoading(false);
        },
      },
    );
  };

  useEffect(() => {
    if (ctrlInfo.chapterId && subject) {
      getControlQuestions();
    }
  }, [ctrlInfo.chapterId, subject]);

  const copyControl = () => {
    let text = '';
    controle?.exercises.forEach((exo) => {
      text += `${exo.title}\n`;
      exo.questions.forEach((question, index) => {
        text += ` ${index + 1}. ${question.questionContent}\n`;
      });
    });
    navigator.clipboard.writeText(text);
  };
  const printCtrl = useReactToPrint({
    contentRef: ctrlRef,
  });

  return (
    <Tabs
      defaultValue="sujetducontrole"
      className="m-2 mx-8 xl:h-[70vh] relative"
    >
      <TabsList
        className={`flex justify-start ${dir === 'rtl' ? 'flex-row-reverse' : ''} p-0`}
      >
        <TabsTrigger
          value="sujetducontrole"
          className="w-44 border-dinoBotBlue border -mb-2 border-b-dinoBotBlue rounded-b-none underline text-dinoBotBlue data-[state=active]:bg-dinoBotBlue data-[state=active]:text-white"
        >
          {t('controlSubject')}
        </TabsTrigger>
        <TabsTrigger
          value="macopie"
          className={`w-44 border-dinoBotBlue border -mb-2 border-b-dinoBotBlue rounded-b-none underline text-dinoBotBlue data-[state=active]:bg-dinoBotBlue data-[state=active]:text-white`}
        >
          {t('myCopy')}
        </TabsTrigger>
        <TabsTrigger
          value="correction"
          className={`w-44 border-dinoBotBlue border -mb-2 border-b-dinoBotBlue rounded-b-none underline text-dinoBotBlue data-[state=active]:bg-dinoBotBlue data-[state=active]:text-white`}
          disabled={isRunning || isLoading}
        >
          {t('correction')}
        </TabsTrigger>
      </TabsList>
      <TabsContent
        value="sujetducontrole"
        className="m-0 p-4 xl:overflow-y-scroll h-full border border-dinoBotBlue "
      >
        <div ref={ctrlRef}>
          {isLoading ? (
            <Loading />
          ) : (
            controle?.exercises.map((answer) => (
              <div
                key={answer.id}
                className={`flex flex-col ${dir === 'ltr' ? '' : 'text-right'} `}
              >
                <h2 className="text-lg font-bold text-sky-900 ">
                  {answer.title}
                </h2>
                <div className="flex flex-col m-2">
                  {answer.questions.map((content) => (
                    <div key={content.id} className="flex flex-col my-1">
                      <GenerateFormatedText
                        className="text-dinoBotGray font-semibold"
                        content={content.id + 1 + ')'}
                      >
                        {content.questionContent}
                      </GenerateFormatedText>
                      {content.desmosCode && content.desmosCode.expressions ? (
                        <DesmosUi
                          data={content.desmosCode}
                          className="w-96 h-80"
                        />
                      ) : (
                        ''
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>
        <div className="absolute top-[-9999px]">
          <div ref={ctrlRef} className="p-4">
            <div className="w-full flex gap-2 items-center justify-center py-3 bg-dinoBotVibrantBlue/5">
              <img src="/dinobot-logo.svg" alt="logo" width={48} height={48} />
              <div className="text-2xl text-dinoBotBlue font-semibold">
                DinoBot
              </div>
            </div>
            {isLoading ? (
              <Loading />
            ) : (
              controle?.exercises.map((answer) => (
                <div
                  key={answer.id}
                  className={`flex flex-col ${dir === 'ltr' ? '' : 'text-right'} `}
                >
                  <h2 className="text-lg font-bold text-sky-900 ">
                    {answer.title}
                  </h2>
                  <div className="flex flex-col m-2">
                    {answer.questions.map((content) => (
                      <div key={content.id} className="flex flex-col my-1">
                        <GenerateFormatedText
                          className="text-dinoBotGray font-semibold"
                          content={content.id + 1 + ''}
                        >
                          {content.questionContent}
                        </GenerateFormatedText>
                        {content.desmosCode &&
                        content.desmosCode.expressions ? (
                          <DesmosUi
                            data={content.desmosCode}
                            className="w-96 h-80"
                            style={{
                              pageBreakInside: 'avoid',
                            }}
                          />
                        ) : (
                          ''
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </TabsContent>
      <CtrlOptions onCopy={copyControl} onPrint={printCtrl} />
      <TabsContent
        value="macopie"
        className="m-0 p-8 overflow-y-scroll h-full border border-dinoBotBlue"
      >
        <div
          className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} `}
          ref={ctrlRef}
        >
          <div className="flex justify-between">
            <h3>Student Name</h3>
            <h3>{moment().format('DD/MM/yyyy')}</h3>
          </div>
          <div className="flex flex-col items-center">
            <h2 className="underline">
              {t('controlOf') +
                ' ' +
                getLangProps({
                  obj: domain!,
                  base: 'name',
                  lang: i18n.language,
                })}
            </h2>
          </div>
          <div className="mt-5 ml-5">
            {isLoading ? (
              <Loading />
            ) : (
              controle?.exercises.map((answer) => (
                <div key={answer.id} className="flex flex-col ">
                  <h2 className="text-lg font-bold text-sky-900">
                    {answer.title}
                  </h2>
                  <div className="flex flex-col m-5">
                    {answer.questions.map((content) => (
                      <div key={content.id} className="flex flex-col my-1">
                        <GenerateFormatedText className="text-dinoBotGray font-semibold">
                          {content.id + 1 + ')' + content.questionContent}
                        </GenerateFormatedText>
                        {content.desmosCode &&
                        content.desmosCode.expressions ? (
                          <DesmosUi
                            data={content.desmosCode}
                            className="w-96 h-80"
                          />
                        ) : (
                          ''
                        )}
                        {content.answer ? (
                          <MemoizedReactMarkdown
                            remarkPlugins={[remarkGfm, remarkMath]}
                            rehypePlugins={[rehypeRaw, rehypemathjax]}
                            components={{
                              p({ children }) {
                                return (
                                  <p
                                    className={cn(
                                      'text-dinoBotGray font-semibold mt-2 mb-4 text-left',
                                      'mb-2 last:mb-0',
                                    )}
                                  >
                                    {children}
                                  </p>
                                );
                              },
                            }}
                          >
                            {content.answer}
                          </MemoizedReactMarkdown>
                        ) : (
                          <div className="text-red-600 p-2 rounded-lg">
                            {t('noAnswer')}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
        <div className="absolute top-[-9999px]">
          <div
            className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} `}
            ref={ctrlRef}
          >
            <div className="flex justify-between">
              <h3>
                {user.firstName} {user.lastName}
              </h3>
              <h3>{moment().format('DD/MM/yyyy')}</h3>
            </div>
            <div className="flex flex-col items-center">
              <h2 className="underline">
                {t('controlOf') +
                  ' ' +
                  getLangProps({
                    obj: domain!,
                    base: 'name',
                    lang: locale,
                  })}
              </h2>
            </div>
            <div className="mt-5 ml-5">
              {isLoading ? (
                <Loading />
              ) : (
                controle?.exercises.map((answer) => (
                  <div key={answer.id} className="flex flex-col ">
                    <h2 className="text-lg font-bold text-sky-900">
                      {answer.title}
                    </h2>
                    <div className="flex flex-col m-5">
                      {answer.questions.map((content) => (
                        <div key={content.id} className="flex flex-col my-1">
                          <GenerateFormatedText
                            className="text-dinoBotGray font-semibold"
                            content={content.id + 1 + ')'}
                          >
                            {content.questionContent}
                          </GenerateFormatedText>
                          {content.desmosCode &&
                          content.desmosCode.expressions ? (
                            <DesmosUi
                              data={content.desmosCode}
                              className="w-96 h-80"
                              style={{
                                pageBreakInside: 'avoid',
                              }}
                            />
                          ) : (
                            ''
                          )}
                          {content.answer ? (
                            <MemoizedReactMarkdown
                              remarkPlugins={[remarkGfm, remarkMath]}
                              rehypePlugins={[rehypeRaw, rehypemathjax]}
                              components={{
                                p({ children }) {
                                  return (
                                    <p
                                      className={cn(
                                        'text-dinoBotGray font-semibold mt-2 mb-4 text-left',
                                        'mb-2 last:mb-0',
                                      )}
                                    >
                                      {children}
                                    </p>
                                  );
                                },
                              }}
                            >
                              {content.answer}
                            </MemoizedReactMarkdown>
                          ) : (
                            <div className="text-red-600 p-2 rounded-lg">
                              {t('noAnswer')}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </TabsContent>
      <TabsContent
        value="correction"
        className="m-0 p-8 overflow-y-scroll h-full border border-dinoBotBlue"
      >
        <div
          className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} `}
          ref={ctrlRef}
        >
          <div className="flex justify-between">
            <h3>Student Name</h3>
            <h3>{moment().format('DD/MM/yyyy')}</h3>
          </div>
          <div className="flex flex-col items-center">
            <h2 className="underline">
              {t('controlOf') +
                ' ' +
                getLangProps({
                  obj: domain!,
                  base: 'name',
                  lang: i18n.language,
                })}
            </h2>
          </div>
          <div className="mt-5 ml-5">
            {isLoading ? (
              <Loading />
            ) : (
              controle?.exercises.map((answer) => (
                <div key={answer.id} className="flex flex-col ">
                  <h2 className="text-lg font-bold text-sky-900">
                    {answer.title}
                  </h2>
                  <div className="flex flex-col m-5">
                    {answer.questions.map((content) => (
                      <div key={content.id} className="flex flex-col my-1">
                        <GenerateFormatedText className="text-dinoBotGray font-semibold">
                          {content.id + 1 + ')' + content.questionContent}
                        </GenerateFormatedText>
                        {content.desmosCode &&
                        content.desmosCode.expressions ? (
                          <DesmosUi
                            data={content.desmosCode}
                            className="w-96 h-80"
                          />
                        ) : (
                          ''
                        )}
                        {content.feedback && (
                          <GenerateFormatedText className="text-red-600 p-2 rounded-lg">
                            {content.feedback}
                          </GenerateFormatedText>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
        <div className="absolute top-[-9999px]">
          <div
            className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} `}
            ref={ctrlRef}
          >
            <div className="flex justify-between">
              <h3>
                {user.firstName} {user.lastName}
              </h3>
              <h3>{moment().format('DD/MM/yyyy')}</h3>
            </div>
            <div className="flex flex-col items-center">
              <h2 className="underline">
                {t('controlOf') +
                  ' ' +
                  getLangProps({
                    obj: domain!,
                    base: 'name',
                    lang: locale,
                  })}
              </h2>
            </div>
            <div className="mt-5 ml-5">
              {isLoading ? (
                <Loading />
              ) : (
                controle?.exercises.map((answer) => (
                  <div key={answer.id} className="flex flex-col ">
                    <h2 className="text-lg font-bold text-sky-900">
                      {answer.title}
                    </h2>
                    <div className="flex flex-col m-5">
                      {answer.questions.map((content) => (
                        <div key={content.id} className="flex flex-col my-1">
                          <GenerateFormatedText
                            className="text-dinoBotGray font-semibold"
                            content={content.id + 1 + ')'}
                          >
                            {content.questionContent}
                          </GenerateFormatedText>
                          {content.desmosCode &&
                          content.desmosCode.expressions ? (
                            <DesmosUi
                              data={content.desmosCode}
                              className="w-96 h-80"
                              style={{
                                pageBreakInside: 'avoid',
                              }}
                            />
                          ) : (
                            ''
                          )}

                          {content.feedback && (
                            <GenerateFormatedText className="text-red-600 p-2 rounded-lg">
                              {content.feedback}
                            </GenerateFormatedText>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default ControlPreview;

const GenerateFormatedText = ({
  children,
  className,
  content = '',
}: {
  children: string;
  className: string;
  content?: string;
}) => {
  return (
    <MemoizedReactMarkdown
      remarkPlugins={[remarkGfm, remarkMath]}
      rehypePlugins={[rehypeRaw, rehypemathjax]}
      components={{
        p({ children }) {
          return <p className={cn(className, 'mb-2 last:mb-0')}>{children}</p>;
        },
      }}
    >
      {`${content} ${children}`}
    </MemoizedReactMarkdown>
  );
};
interface CtrlOptionsProps {
  onCopy?: () => void;
  onGenerate?: () => void;
  onPrint?: () => void;
  onDownload?: () => void;
}

function CtrlOptions({ onCopy, onPrint }: CtrlOptionsProps) {
  const { t, i18n } = useTranslation(['app/mode/controle/controlPreview']);
  const [animGenerate, setAnimGenerate] = useState(false);
  const [animCopy, setAnimCopy] = useState(false);
  const [animDownload, setAnimDownload] = useState(false);

  const dir = getLangDir(i18n.language);
  useEffect(() => {
    if (animGenerate) setTimeout(() => setAnimGenerate(false), 1500);
    if (animCopy) setTimeout(() => setAnimCopy(false), 1500);
    if (animDownload) setTimeout(() => setAnimDownload(false), 1500);
  }, [animGenerate, animCopy, animDownload]);

  return (
    <div
      className={`flex gap-2 absolute -bottom-16 ${dir === 'ltr' ? 'right-0' : 'left-0'} `}
    >
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            className="size-5 p-0 hover:bg-background"
            onClick={
              onCopy
                ? () => {
                    setAnimCopy(true);
                    onCopy();
                  }
                : undefined
            }
          >
            {!animCopy ? <Clipboard /> : <Check className="animate-fade-in" />}
            <span className="sr-only">Copier L&apos;exercice</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent className="bg-dinoBotBlue">
          {t('copyExercise')}
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            className="size-5 p-0 hover:bg-background"
            onClick={onPrint ?? undefined}
          >
            <Printer />
            <span className="sr-only">Imprimer L&apos;exercice</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent className="bg-dinoBotBlue">
          {t('printExercise')}
        </TooltipContent>
      </Tooltip>
    </div>
  );
}
