import { useEffect } from 'react'
import CoursesList from './components/courses-list'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { spinner } from '@dinobot/components-ui'
import { useFeatureRoutes } from '../../hooks/useFeatureFlags'

function MyCourses() {
    const { t } = useTranslation(['app/courses/index'])
    const navigate = useNavigate()
    const { data: featureFlags, isLoading } = useFeatureRoutes('STUDENT_CLASSES_VIEW')

    useEffect(() => {
        if (
            !isLoading &&
            featureFlags &&
            Array.isArray(featureFlags) &&
            featureFlags[0] != null
        ) {
            navigate(`/${featureFlags[0]}`)
        }
    }, [featureFlags, isLoading, navigate])

    // Determine if we should show a loading state. This is true if we are fetching data
    // or if we have the data and are about to redirect.
    const showLoading =
        isLoading ||
        (featureFlags && Array.isArray(featureFlags) && featureFlags[0] != null)

    if (showLoading) {
        return (
            <div className="flex items-center justify-center h-screen w-full">
                {spinner}
            </div>
        )
    }

    // If we are not loading and not redirecting, render the page content.
    return (
        <div className="size-full bg-[#fafafa] p-6">
            <h1 className="text-dinoBotDarkGray text-3xl font-bold m-10">
                {t('title')}
            </h1>
            <CoursesList />
        </div>
    )
}

export default MyCourses
