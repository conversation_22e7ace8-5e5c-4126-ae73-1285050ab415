import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../contexts/AppContext';
import { PlannedEvaluationPartialWithRelations } from '@dinobot/prisma'

export const useNotation = (courseId: string | undefined, order?: 'asc') => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['notation', courseId, order],
        queryFn: async () => {
            if (!courseId) return []

            const queryParams = order ? { order } : {}
            const response = await apiClient.get(`/api/evaluation-scheduler/notation/class/${courseId}`, {
                params: queryParams
            })
            return response.data as PlannedEvaluationPartialWithRelations[]
        },
        enabled: !!courseId && !!apiClient
    })
}
