import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'

export const useChaptersByDomainAndLevel = (domainId: number | undefined, levelId: number | undefined) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['chapters', domainId, levelId],
        queryFn: async () => {
            if (!domainId || !levelId || !apiClient) return []

            const response = await apiClient.get(`/api/training-mode/chapters/${domainId}/${levelId}`)
            return response.data
        },
        enabled: !!domainId && !!levelId && !!apiClient
    })
}
