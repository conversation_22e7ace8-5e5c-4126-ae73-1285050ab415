// import { usePathname, useRouter } from '@/i18n/routing'
import { selectUseAvatarStore ,useChatStore,useStreamingStore} from '@dinobot/stores'
import { Session } from '@dinobot/utils'
import { Message, UIMessage } from 'ai'
import { nanoid } from 'nanoid'
import React, { useEffect } from 'react'
import Cookies from 'js-cookie';
import { useLocation, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

interface customUiMessage extends UIMessage {
    file: unknown
}

type CheckMessageChatProps = {
    messages: UIMessage[]
    session?: Session
    setMessages: (
        messages: Message[] | ((messages: Message[]) => Message[])
    ) => void
    id: string
    previousPath: string
    setPreviousPath: (previousPath: string) => void
    status: 'error' | 'submitted' | 'streaming' | 'ready'
}

const CheckMessageChat = ({
    messages,
    session,
    setMessages,
    id,
    previousPath,
    setPreviousPath,
    status
}: CheckMessageChatProps) => {
    const {t} = useTranslation(['app/chat'],{keyPrefix:"[id]"})
    
    const navigate = useNavigate();
    
    const setKnowledgeBaseModeContent =
        selectUseAvatarStore.use.setKnowledgeBaseModeContent()
    const { setIsStreaming } = useStreamingStore()
    const {
        refreshChatsList,
        setRefreshChatsList,
        refreshFilesList,
        setRefreshFilesList
    } = useChatStore()
    const path = useLocation().pathname

    useEffect(() => {
        const messagesLength = messages?.length
        const includesFile = (messages[messagesLength - 2] as customUiMessage)
            ?.file
        if (
            messagesLength > 0 &&
            messagesLength % 2 === 0 &&
            !refreshChatsList
        ) {
            setRefreshChatsList(true)
            if (includesFile && !refreshFilesList) {
                setRefreshFilesList(true)
            }
        }
        setKnowledgeBaseModeContent(
            `voicie le chat : ${JSON.stringify(messages)}`
        )
        if (messagesLength === 4 && !session?.user) {
            Cookies.set('restrictFreeUser', 'yes')
            setIsStreaming(false)
            setMessages(currentMessages => [
                ...currentMessages,
                {
                    id: nanoid(),
                    role: 'assistant',
                    content: t('continue')
                }
            ])
        }
    }, [messages])

    useEffect(() => {
        if (session?.user) {
            if (previousPath.includes('chat') && path === '/') {
                setMessages([])
                navigate('/')
            }
            if (
                !path.includes('chat') &&
                messages.length === 2 &&
                status === 'ready'
            ) {
                setTimeout(() => {
                    if (Cookies.get('feature') === 'Chat')
                        window.history.replaceState(
                            {},
                            '',
                            `/chat/${id}`
                        )
                    if (Cookies.get('feature') === 'Exam')
                        window.history.replaceState({}, '', `exam/chat/${id}`)
                }, 3000)
            }
        }
        setPreviousPath(path)
    }, [id, session?.user, messages, previousPath, status])

    return <></>
}

export default CheckMessageChat
