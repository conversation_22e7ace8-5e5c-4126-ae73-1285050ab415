import { StreamableValue, readStreamableValue } from 'ai/rsc'
import { useEffect, useState } from 'react'
import { useStreamingStore } from '@dinobot/stores'

const useStreamableText = (
    content: string | StreamableValue<string>
) => {
    const [rawContent, setRawContent] = useState(
        typeof content === 'string' ? content : ''
    )

    const { setIsStreaming } = useStreamingStore()

    useEffect(() => {
        try {
            ;(async () => {
                if (typeof content === 'object') {
                    let value = ''
                    for await (const delta of readStreamableValue(content)) {
                        if (typeof delta === 'string') {
                            setRawContent((value = value + delta))
                        }
                    }
                }
                setIsStreaming(false)
            })()
        } catch {
            //console.log("an error occurred in the useStreamableText hook")
            setIsStreaming(false)
        }
    }, [content])

    return rawContent
}

export default useStreamableText
