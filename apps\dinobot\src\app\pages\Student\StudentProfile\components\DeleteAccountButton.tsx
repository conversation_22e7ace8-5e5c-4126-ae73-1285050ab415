import { useReducer } from 'react'
import { <PERSON><PERSON> ,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    DialogDescription,
    Di<PERSON><PERSON>ooter,
    Di<PERSON>Header,
    DialogTitle,
    Input 
} from '@dinobot/components-ui'
import { toast } from 'sonner'
import { TriangleAlert } from 'lucide-react'
import { UserType } from '@dinobot/utils'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { useStudentProfile } from '../hooks/useStudentProfile'
import { useStudentProfileStore } from '../store/StudentProfile.store'

// Define the state type
type State = {
    showInfoDialog: boolean
    showPasswordDialog: boolean
    password: string
    isSubmitting: boolean
    passwordError: string
}

// Define the action types
type Action =
    | { type: 'SHOW_INFO_DIALOG' }
    | { type: 'SHOW_PASSWORD_DIALOG' }
    | { type: 'HIDE_DIALOGS' }
    | { type: 'SET_PASSWORD'; payload: string }
    | { type: 'SET_PASSWORD_ERROR'; payload: string }
    | { type: 'SET_SUBMITTING'; payload: boolean }
    | { type: 'RESET_FORM' }

// Define the reducer function
function reducer(state: State, action: Action): State {
    switch (action.type) {
        case 'SHOW_INFO_DIALOG':
            return { ...state, showInfoDialog: true }
        case 'SHOW_PASSWORD_DIALOG':
            return { ...state, showInfoDialog: false, showPasswordDialog: true }
        case 'HIDE_DIALOGS':
            return {
                ...state,
                showInfoDialog: false,
                showPasswordDialog: false,
                password: '',
                passwordError: ''
            }
        case 'SET_PASSWORD':
            return { ...state, password: action.payload }
        case 'SET_PASSWORD_ERROR':
            return { ...state, passwordError: action.payload }
        case 'SET_SUBMITTING':
            return { ...state, isSubmitting: action.payload }
        case 'RESET_FORM':
            return {
                ...state,
                password: '',
                passwordError: '',
                isSubmitting: false
            }
        default:
            return state
    }
}

export default function DeleteAccountButton() {
    const { t } = useTranslation(['app/chat'], { keyPrefix: 'profile.delete-account' })
    const { user } = useStudentProfileStore()
    const navigate = useNavigate()
    const { deleteAccount, isDeletingAccount } = useStudentProfile()

    // Initialize state with useReducer
    const [state, dispatch] = useReducer(reducer, {
        showInfoDialog: false,
        showPasswordDialog: false,
        password: '',
        isSubmitting: false,
        passwordError: ''
    })

    const handleDeleteRequest = () => {
        dispatch({ type: 'SHOW_INFO_DIALOG' })
    }

    const handleContinueDelete = () => {
        dispatch({ type: 'SHOW_PASSWORD_DIALOG' })
    }

    const handleCancel = () => {
        dispatch({ type: 'HIDE_DIALOGS' })
    }

    const handleConfirmDelete = async () => {
        if (!state.password) {
            dispatch({
                type: 'SET_PASSWORD_ERROR',
                payload: t('password-required')
            })
            return
        }

        dispatch({ type: 'SET_SUBMITTING', payload: true })
        dispatch({ type: 'SET_PASSWORD_ERROR', payload: '' })

        try {
            const response = await deleteAccount()
            if (response.type === 'success') {
                toast.success(t('account-deletion-requested'))
                navigate('/account-deletion-requested')
            } else {
                throw new Error(response.message || 'Error deleting account')
            }
        } catch (error) {
            toast.error(t('error-occurred'))
            dispatch({
                type: 'SET_PASSWORD_ERROR',
                payload: (error as { message: string }).message || t('error-occurred')
            })
        } finally {
            dispatch({ type: 'SET_SUBMITTING', payload: false })
        }
    }

    return (
        <>
            <section className="w-full flex justify-center">
                <Button
                    variant="destructive"
                    className="w-fit"
                    onClick={handleDeleteRequest}
                >
                    {t('delete-account-button')}
                </Button>
            </section>
            {/* Info Dialog */}
            <Dialog
                open={state.showInfoDialog}
                onOpenChange={open =>
                    dispatch({
                        type: open ? 'SHOW_INFO_DIALOG' : 'HIDE_DIALOGS'
                    })
                }
            >
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle className="text-destructive flex justify-center text-dinoBotDarkGray">
                            {t('common.warning-title')}
                        </DialogTitle>
                        <DialogDescription className="pt-4">
                            <header className="text-dinoBotRed font-bold flex items-center gap-2 my-4">
                                <TriangleAlert className="text-dinoBotRed" />
                                {t('common.warning-message')}
                            </header>
                            <p className="mb-4">
                                {t('common.permanent-deletion-warning')}
                            </p>

                            <div className="mb-4">
                                <h3 className="font-bold mb-2">
                                    {t('common.items-deleted')}
                                </h3>
                                <ul className="list-disc pl-5 space-y-1">
                                    <li>{t('student.item1')}</li>
                                    <li>{t('student.item2')}</li>
                                </ul>
                            </div>

                            <div className="mb-4">
                                <h3 className="font-bold mb-2">
                                    {t('common.consequences')}
                                </h3>
                                <ul className="list-disc pl-5 space-y-1">
                                    <li>{t('common.consequence1')}</li>
                                    <li>{t('common.consequence2')}</li>
                                    <li>{t('common.consequence3')}</li>
                                </ul>
                            </div>

                            <p className="font-bold text-destructive">
                                {t('common.irreversible-action')}
                            </p>
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="flex flex-row justify-between sm:justify-between">
                        <Button
                            type="button"
                            variant="secondary"
                            onClick={handleCancel}
                        >
                            {t('cancel')}
                        </Button>
                        <Button
                            type="button"
                            variant="destructive"
                            onClick={handleContinueDelete}
                        >
                            {t('continue-deletion')}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Password Confirmation Dialog */}
            <Dialog
                open={state.showPasswordDialog}
                onOpenChange={open => {
                    if (!open) {
                        dispatch({ type: 'HIDE_DIALOGS' })
                    }
                }}
            >
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>{t('confirm-deletion')}</DialogTitle>
                        <DialogDescription className="pt-4">
                            <p className="mb-4">
                                {t('enter-password-to-confirm')}
                            </p>

                            <div className="space-y-4">
                                <Input
                                    type="password"
                                    placeholder={t('password-placeholder')}
                                    value={state.password}
                                    onChange={e =>
                                        dispatch({
                                            type: 'SET_PASSWORD',
                                            payload: e.target.value
                                        })
                                    }
                                    className={
                                        state.passwordError
                                            ? 'border-destructive'
                                            : ''
                                    }
                                />
                                {state.passwordError && (
                                    <p className="text-destructive text-sm">
                                        {state.passwordError}
                                    </p>
                                )}

                                <p className="text-sm text-muted-foreground">
                                    {t('immediate-suspension-warning')}
                                </p>
                            </div>
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="flex flex-row justify-between sm:justify-between">
                        <Button
                            type="button"
                            variant="secondary"
                            onClick={handleCancel}
                            disabled={state.isSubmitting || isDeletingAccount}
                        >
                            {t('cancel')}
                        </Button>
                        <Button
                            type="button"
                            variant="destructive"
                            onClick={handleConfirmDelete}
                            disabled={state.isSubmitting || isDeletingAccount}
                        >
                            {state.isSubmitting || isDeletingAccount
                                ? t('processing')
                                : t('confirm-deletion-button')}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    )
}