export type ChatProps = {
    initialMessages?: Message[]
    id?: string
    session?: Session
    missingKeys: string[]
    ttsMode?: boolean
}

export type Message = {
    id: string
    content: string
    role: 'user' | 'assistant' | 'system'
    createdAt: Date
    file?: unknown
}

export type UIMessage = Message & {
    file: unknown
}

export type Session = {
    user?: {
        id: string
        email: string
        name?: string
        type?: 'student' | 'teacher'
    }
}

export type ChatFormData = {
    input: string
    imageFile: string
    fileExtension: string
}

export type ChatState = {
    formData: ChatFormData
    messages: UIMessage[]
    isLoading: boolean
    status: 'submitted' | 'streaming' | 'ready' | 'error'
}

export interface MessageWithFile {
    id: string
    role: 'user' | 'assistant' | 'system'
    content: string
    createdAt?: Date
    file?: {
        id?: string
        name: string
        file: string
        createdAt: Date
    }
    audioFile?: {
        id?: string
        name: string
        audioFile: string
        createdAt: Date
    }
}

export interface ChatWithMessages {
    id: string
    title: string
    userId: string
    path: string
    topic: string
    exerciseId?: string | null
    createdAt: Date
    updatedAt: Date
    pinned: boolean
    messages: MessageWithFile[]
}

export interface ChatByIdPageParams {
    params: {
        locale: string;
        id: string;
    };
}