//Hooks
import * as React from 'react'

//UI
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@dinobot/components-ui'

//Icons
import { FileText, MessageCircleMore, Star, Timer } from 'lucide-react'

//Stores
import { useCtrlModeStore, useLocalStorageStore, useExamsStore } from '@dinobot/stores'

import Cookies from 'js-cookie'
import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../contexts/AppContext'
import { ExamTypeType, User } from '@dinobot/prisma'
import { useNavigate, useRouteLoaderData } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useFeatureFlags } from '../../../hooks/useFeatureFlags'

export function SelectFeature() {
    const { chatModeEnabled, examModeEnabled, exerciseModeEnabled, evaluationModeEnabled } = useFeatureFlags()
    const navigate = useNavigate()
    const { setopenExamsPopup, setExercise } = useExamsStore()
    const reset = useCtrlModeStore(state => state.reset)
    const { t } = useTranslation(['app/sub-headers'])
    const authApiClient = useAuthApiClient()
    
    const feature = Cookies.get('feature')
    const {user}= useRouteLoaderData('user') as {user:User}
    
    const domainId = useLocalStorageStore(state => state.domainId)

    // Hook pour récupérer les examTypes
    const { data: examType = [] } = useQuery({
        queryKey: ['examTypes', user?.levelId, domainId],
        queryFn: async (): Promise<ExamTypeType[]> => {
            const response = await authApiClient.get(`/api/domain-level/exam-types/${user?.levelId}/${domainId}`);
            return response.data || [];
        },
        enabled: !!(domainId && user?.levelId),
        staleTime: 5 * 60 * 1000, // 5 minutes
    });

    React.useEffect(() => {
        //console.log("The actual feature stored in the cookies is " + feature)
        if (!feature) {
            Cookies.set('feature', 'Chat')
        }
        // else handleRoutesChange(cookies.get("feature"))
    }, [feature])

    const handleFeatureChange = (value: string) => {
        if (value === 'Exam' || value === 'ExamE') {
            Cookies.set('feature', value)
            Cookies.set('mode', 'GPT 4-o')
            navigate('/exam')
            setopenExamsPopup(true)
            return
        } else if (value === 'Exo') {
            Cookies.set('feature', value)
            Cookies.set('mode', 'GPT 4-o')
            navigate('/exercise')
            reset()
            return
        } else if (value === 'Ctrl') {
            Cookies.set('feature', value)
            Cookies.set('mode', 'GPT 4-o')
            navigate('/create-controle')
            reset()
            return
        }

        setExercise(null)
        Cookies.set('feature', value)
        // openSidebar()
        // openFilesSidebar()
        navigate('/')
        //window.location.reload()
    }

    const getCurrentFeatureTriggerClass = () => {
        switch (feature) {
            case 'Chat':
                return "w-[160px] [&>svg]:stroke-[3.5] [&>svg]:w-6 [&>svg]:h-6 text-white font-semibold hover:bg-dinoBotBlue bg-dinoBotBlue border-dinoBotBlue"
            case 'Exam':
            case 'ExamE':
                return "w-[160px] [&>svg]:stroke-[3.5] [&>svg]:w-6 [&>svg]:h-6 text-white font-semibold hover:bg-dinoBotYellow bg-dinoBotYellow border-dinoBotYellow"
            case 'Ctrl':
                return "w-[160px] [&>svg]:stroke-[3.5] [&>svg]:w-6 [&>svg]:h-6 text-white font-semibold hover:bg-dinoBotVividOrange bg-dinoBotVividOrange border-dinoBotVividOrange"
            case 'Exo':
                return "w-[160px] [&>svg]:stroke-[3.5] [&>svg]:w-6 [&>svg]:h-6 text-white font-semibold hover:bg-dinoBotCyan bg-dinoBotCyan border-dinoBotCyan"
            default:
                return "w-[160px] [&>svg]:stroke-[3.5] [&>svg]:w-6 [&>svg]:h-6 text-white font-semibold hover:bg-dinoBotCyan bg-dinoBotCyan border-dinoBotCyan"
        }
    }

    const getCurrentFeatureLabel = () => {
        switch (feature) {
            case 'Chat':
                return (
                    <div className="flex items-center gap-2">
                        <MessageCircleMore className="w-5 h-5" />
                        <span>{t('chat.title')}</span>
                    </div>
                )
            case 'Exam':
                return (
                    <div className="flex items-center gap-2">
                        <Star className="w-5 h-5" />
                        <span>{examType[0] ? t(`exam.title.${examType[0]}`) : t('exam.title')}</span>
                    </div>
                )
            case 'ExamE':
                return (
                    <div className="flex items-center gap-2">
                        <Star className="w-5 h-5" />
                        <span>{examType[1] ? t(`exam.title.${examType[1]}`) : t('exam.title')}</span>
                    </div>
                )
            case 'Ctrl':
                return (
                    <div className="flex items-center gap-2">
                        <Timer className="w-5 h-5" />
                        <span>{t('controle.title')}</span>
                    </div>
                )
            case 'Exo':
                return (
                    <div className="flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        <span>{t('exo.title')}</span>
                    </div>
                )
            default:
                return (
                    <div className="flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        <span>mode exercise</span>
                    </div>
                )
        }
    }

    return (
        <Select onValueChange={handleFeatureChange} value={feature}>
            <SelectTrigger className={getCurrentFeatureTriggerClass()}>
                <SelectValue placeholder={getCurrentFeatureLabel()} />
            </SelectTrigger>
            <SelectContent>
                {chatModeEnabled && (
                    <SelectItem value="Chat" className="focus:bg-dinoBotBlue focus:text-white">
                        <div className="flex items-center gap-2">
                            <MessageCircleMore className="w-4 h-4" />
                            <span>{t('chat.title')}</span>
                        </div>
                    </SelectItem>
                )}
                {examModeEnabled && (
                    <>
                        {examType && examType[0] ? (
                            <SelectItem value="Exam" className="focus:bg-dinoBotYellow focus:text-white">
                                <div className="flex items-center gap-2">
                                    <Star className="w-4 h-4" />
                                    <span>{t(`exam.title.${examType[0]}`)}</span>
                                </div>
                            </SelectItem>
                        ) : null}
                        {examType && examType[1] ? (
                            <SelectItem value="ExamE" className="focus:bg-dinoBotYellow focus:text-white">
                                <div className="flex items-center gap-2">
                                    <Star className="w-4 h-4" />
                                    <span>{t(`exam.title.${examType[1]}`)}</span>
                                </div>
                            </SelectItem>
                        ) : null}
                    </>
                )}
                {evaluationModeEnabled && (
                    <SelectItem value="Ctrl" className="focus:bg-dinoBotVividOrange focus:text-white">
                        <div className="flex items-center gap-2">
                            <Timer className="w-4 h-4" />
                            <span>{t('controle.title')}</span>
                        </div>
                    </SelectItem>
                )}
                {exerciseModeEnabled && (
                    <SelectItem value="Exo" className="focus:bg-dinoBotCyan focus:text-white">
                        <div className="flex items-center gap-2">
                            <FileText className="w-4 h-4" />
                            <span>{t('exo.title')}</span>
                        </div>
                    </SelectItem>
                )}
            </SelectContent>
        </Select>
    )
}