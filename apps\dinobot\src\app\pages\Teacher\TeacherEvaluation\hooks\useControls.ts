import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { ControlPartialWithRelations } from '@dinobot/prisma'
import type { UseControlsParams } from '../teacherEvaluation.types'

export const useControls = (params: UseControlsParams = {}) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['controls', params],
        queryFn: async (): Promise<ControlPartialWithRelations[]> => {
            if (!apiClient) return []
            
            const searchParams = new URLSearchParams()
            Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined) {
                    searchParams.append(key, value.toString())
                }
            })
            
            const response = await apiClient.get(`/api/control-mode/?${searchParams.toString()}`)
            return response.data
        },
        enabled: !!apiClient
    })
}