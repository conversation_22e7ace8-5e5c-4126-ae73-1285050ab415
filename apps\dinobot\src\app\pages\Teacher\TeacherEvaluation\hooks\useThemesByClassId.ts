import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { ThemePartial } from '@dinobot/prisma'

export const useThemesByClassId = (classId: string | undefined) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['themes-by-class-id', classId],
        queryFn: async (): Promise<ThemePartial[]> => {
            if (!classId || !apiClient) return []
            
            const response = await apiClient.get(`/api/themes/class/${classId}`)
            return response.data
        },
        enabled: !!classId && !!apiClient
    })
}