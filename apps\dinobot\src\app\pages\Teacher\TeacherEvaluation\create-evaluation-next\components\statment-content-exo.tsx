import { cn, isEmptyOrNull } from '@dinobot/utils'
import { useTranslation } from 'react-i18next'
import React, { useReducer } from 'react'
import { getLangDir } from 'rtl-detect'
import EditorQuill from './editeur/editor'
import { Button, MemoizedReactMarkdown } from '@dinobot/components-ui'
import { Check, Copy, Keyboard } from 'lucide-react'
import rehypeRaw from 'rehype-raw'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypemathjax from 'rehype-mathjax'
import { Separator } from '@dinobot/components-ui'
import { selectEvaluationParamsStore } from '../../store/evaluation-params.store'

type StatmentContentExoProps = {
    setIsEditeurOpen: (b: boolean) => void
    isEditeurOpen: boolean
    exoNum: number
    title: string
}

const StatmentContentExo = ({
    isEditeurOpen,
    setIsEditeurOpen,
    exoNum,
    title
}: StatmentContentExoProps) => {
    const { t, i18n } = useTranslation('teacher/myClass/evaluation/next/exo/statement')
    const dir = getLangDir(i18n.language)
    const initStateQuestion = {
        qstCortexIsOpen: false,
        copied: false,
        isEditQuestion: false,
        isEditSolution: false
    }
    const statement = selectEvaluationParamsStore.use.statement()
    const [{ qstCortexIsOpen, copied, isEditQuestion }, dispatch] = useReducer(
        (
            state: typeof initStateQuestion,
            change: Partial<typeof initStateQuestion>
        ) => ({ ...state, ...change }),
        initStateQuestion
    )
    const setStatement = selectEvaluationParamsStore.use.setStatement()
    const onCopy = () => {
        dispatch({ copied: true })
        navigator.clipboard.writeText(statement[exoNum])
        setTimeout(() => dispatch({ copied: false }), 1000)
    }
    return (
        <div
            className={cn(
                `col-span-2 gap-2 items-center flex ml-2 h-full`,
                {
                    'hidden peer-data-[state=checked]/sta:flex':
                        title.split('_').length <= 2
                },
                { 'flex-row-reverse': dir === 'rtl' }
            )}
        >
            <Separator
                orientation="vertical"
                className="w-[2px] rounded-sm bg-dinoBotLightGray"
            />
            <div className={cn('flex flex-col gap-1 w-full')}>
                <h3
                    className={cn(
                        'flex gap-2',
                        dir === 'rtl' && 'flex-row-reverse'
                    )}
                    onClick={() => dispatch({ isEditQuestion: false })}
                >
                    {t(title)}
                </h3>
                <div
                    className={cn(
                        'flex gap-2',
                        dir === 'rtl' && 'flex-row-reverse'
                    )}
                >
                    {isEditQuestion ? (
                        <EditorQuill
                            value={statement[exoNum]}
                            theme={'snow'}
                            placeholder={t('placeholder')}
                            onFocuseOut={() =>
                                dispatch({ isEditQuestion: false })
                            }
                            onChange={(_, text) => setStatement(exoNum, text)}
                            cortexOpen={qstCortexIsOpen}
                            onCortexClose={() => {
                                dispatch({ qstCortexIsOpen: false })
                                setIsEditeurOpen(false)
                            }}
                            className={`chat size-full resize-none focus-within:outline-none sm:text-sm bg-dinoBotWhite rounded-lg border border-dinoBotGray/60`}
                        />
                    ) : (
                        <div
                            className="px-3 py-4 size-full resize-none focus-within:outline-none sm:text-sm bg-dinoBotWhite rounded-lg border border-dinoBotGray/60"
                            onClick={() => dispatch({ isEditQuestion: true })}
                        >
                            <MemoizedReactMarkdown
                                remarkPlugins={[remarkGfm, remarkMath]}
                                rehypePlugins={[rehypeRaw, rehypemathjax]}
                                components={{
                                    p({ children }) {
                                        return <p dir={dir}>{children}</p>
                                    }
                                }}
                            >
                                {isEmptyOrNull(statement[exoNum])
                                    ? t('placeholder')
                                    : statement[exoNum]}
                            </MemoizedReactMarkdown>
                        </div>
                    )}
                    <div className="flex flex-col justify-center">
                        <div
                            className={cn(
                                'flex gap-2',
                                dir === 'rtl' && 'flex-row-reverse'
                            )}
                        >
                            <Button
                                onClick={() => onCopy()}
                                variant={'ghost'}
                                className="p-0 flex justify-center items-center size-8"
                            >
                                {copied ? (
                                    <Check className="text-dinoBotCyan" />
                                ) : (
                                    <Copy className="cursor-pointer text-dinoBotSky" />
                                )}
                            </Button>
                            <Button
                                disabled={isEditeurOpen || !isEditQuestion}
                                onClick={() => {
                                    dispatch({ qstCortexIsOpen: true })
                                    setIsEditeurOpen(true)
                                }}
                                variant={'ghost'}
                                className="p-0 flex justify-center items-center size-8 text-dinoBotCyan hover:text-dinoBotCyan hover:bg-dinoBotCyan/10"
                            >
                                <Keyboard />
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default StatmentContentExo
