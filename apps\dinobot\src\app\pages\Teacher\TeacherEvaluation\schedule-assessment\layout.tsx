import React from 'react'
import { useTranslation } from 'react-i18next'

type LayoutProps = {
    children: React.ReactNode
}
const Layout = ({ children }: LayoutProps) => {
    const { t } = useTranslation()
    return (
        <div className="size-full overflow-hidden flex flex-col py-8 gap-6">
            <div className="w-full flex flex-col gap-4 px-24">
                <div className="flex gap-3 items-center">
                    <div className="text-4xl font-bold text-dinoBotGray/90">
                        {t('teacher.myClass.evaluation.schedule.title')}
                    </div>
                </div>
            </div>
            <div className="w-full grow overflow-y-auto px-16">{children}</div>
        </div>
    )
}

export default Layout
