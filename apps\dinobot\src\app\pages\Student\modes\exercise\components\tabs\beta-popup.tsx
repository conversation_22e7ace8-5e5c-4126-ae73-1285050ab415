import { But<PERSON>, <PERSON><PERSON>, DialogContent, DialogTrigger } from '@dinobot/components-ui';
import { Rocket } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

type BetaPopupProps = { children: React.ReactNode; feature: string }

const BetaPopup = ({ children, feature }: BetaPopupProps) => {
    const [isOpen, setIsOpen] = useState(false)
    const { t } = useTranslation(['app/mode'], { keyPrefix: 'train.beta' })
    const betaFeature = `viewed_beta_${feature}`

    useEffect(() => {
        const isOpened = localStorage.getItem(betaFeature)
        if (!!isOpened) {
            setIsOpen(isOpened === 'true' ? false : true)
        } else {
            localStorage.setItem(betaFeature, 'false')
            setIsOpen(true)
        }
    }, [])

    function handleClose() {
        setIsOpen(false)
        localStorage.setItem(`viewed_beta_${feature}`, 'true')
    }

    function handleOpen() {
        const isOpened = localStorage.getItem(betaFeature)
        if (isOpened === 'true') return
        setIsOpen(true)
    }

    return (
        <Dialog open={isOpen} onOpenChange={handleOpen}>
            <DialogTrigger>{children}</DialogTrigger>
            <DialogContent className="[&>button>svg]:hidden">
                <div>
                    {' '}
                    {t('message')}{' '}
                    <Rocket className="inline" color="red" /> <br />{' '}
                    {t('description')}
                </div>
                <div className="flex items-center">
                    <Button
                        onClick={() => handleClose()}
                        className="w-1/4 mx-auto bg-dinoBotBlue hover:bg-dinoBotVibrantBlue"
                    >
                        ok
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}

export default BetaPopup
