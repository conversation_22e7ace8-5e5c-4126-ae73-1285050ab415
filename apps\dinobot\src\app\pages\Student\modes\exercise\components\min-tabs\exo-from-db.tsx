
import React, { useEffect, useState } from 'react'
import { toast } from 'sonner'
import Cookies from 'js-cookie'
import { Domain } from '@dinobot/prisma'
import { Chapter, Part } from '../../trainig.types'
import { selectUseExoModeStore, useAccountStore } from '@dinobot/stores'
import { useExerciseData } from '../../hooks'
import { useTranslation } from 'react-i18next'
import { getLangProps } from '@dinobot/utils'
import { Button, ErrorTooltip, InfoTooltip, Input, MultiSelect, Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue, Slider } from '@dinobot/components-ui'

interface ExoFromDbProps {
    domains: Domain[]
    chapters: Chapter[]
    // getChapters: (domain: string, level: string) => Promise<Chapter[]>
    // getParts: (chapterId: string) => Promise<Part[]>
    onGenerate?: () => Promise<void>
}

function ExoFromDbMin({
    domains,
    chapters,
    onGenerate
}: ExoFromDbProps) {
    const formData = selectUseExoModeStore.use.exoInfoFromDb()
    const handleFormChange = selectUseExoModeStore.use.updateExoInfoFromDb()
    const [parts, setParts] = useState<Part[]>([])
    const [selectedChapter, setSelectedChapter] = useState<string>(
        formData.chapterId
    )
    
    // Use hooks for API calls
    const { usePartsByChapter } = useExerciseData()
    const { data: partsData } = usePartsByChapter(selectedChapter)
    const {t:tMultiSelect,i18n} = useTranslation(['components/multiselect'])
    const [showError, setShowError] = useState<boolean>(false)
    const setMode = selectUseExoModeStore.use.setMode()
    const {t} = useTranslation(['app/mode'],{keyPrefix:"exo.mini-tabs.db"})
    const {t:t2} = useTranslation(['app/mode'],{keyPrefix:"exo.tab.db"})
    const locale = i18n.language
    const { user } = useAccountStore()
    const levelIdFromCookie = Cookies.get('levelId')

    useEffect(() => {
        setMode('FROM_DB')
    }, [])

    // Set default domain if formData.domainName is empty and domains are available
    useEffect(() => {
        if (!formData.domainName && domains.length > 0) {
            handleFormChange('domainName', domains[0].name)
        }
    }, [domains, formData.domainName])

    useEffect(() => {
        if (
            formData.domainName &&
            formData.domainName.length > 0 &&
            levelIdFromCookie
        ) {
            ;(async () => {
                // Get chapters - chapters are now passed as props from parent component
                // which uses the useChaptersByDomainAndLevel hook from useExerciseData
                // Set default chapter if formData.chapterId is empty and chapters are available
                // if (!formData.chapterId && chaps.length > 0) {
                //     setSelectedChapter(chaps[0].id)
                // }
            })()
        } else {
            // setChapters([]) // chapters are passed as props
        }
    }, [formData.domainName, levelIdFromCookie, chapters]) // Added chapters to dependency array

    // Update parts when partsData changes
    useEffect(() => {
        if (partsData) {
            setParts(partsData)
        }
    }, [partsData])

    useEffect(() => {
        if (selectedChapter) {
            handleFormChange('chapterId', selectedChapter)
            //     const parts = await getParts(selectedChapter)
            //     setParts(parts)
            // })()
        }
    }, [selectedChapter])

    const normalizeExerciseNumber = (value: number): number => {
        if (value > 10) return 10
        if (value < 1) return 1
        return value
    }

    const showLimitInfo = (value: number) => {
        if (value > 10) {
            toast.info(t('n10'), { duration: 2500 })
        } else if (value < 1) {
            toast.info(t('n1'), { duration: 2500 })
        }
    }

    const handleNumberChange = (
        field: 'qstNbr' | 'exoNbr',
        value: number,
        defaultValue: number
    ) => {
        try {
            const normalizedValue = normalizeExerciseNumber(value)
            showLimitInfo(value)
            handleFormChange(field, normalizedValue)
        } catch (error) {
            console.log(error)
            handleFormChange(field, defaultValue)
        }
    }

    const handleQuestionNumberChange = (value: number) => {
        handleNumberChange('qstNbr', value, 5)
    }

    const handleExerciseNumberChange = (value: number) => {
        handleNumberChange('exoNbr', value, 3)
    }

    useEffect(() => {
        if (formData.qstNbr) handleQuestionNumberChange(formData.qstNbr)
    }, [formData.qstNbr])

    useEffect(() => {
        if (formData.exoNbr) handleExerciseNumberChange(formData.exoNbr)
    }, [formData.exoNbr])

    const submit = async () => {
        if (formData.chapterId && formData.partIds.length > 0) {
            if (formData.qstNbr && formData.exoNbr) {
                setShowError(false)
                setMode('FROM_DB')
                if (onGenerate) await onGenerate()
            } else {
                toast.error(t('n1&10'))
            }
        } else {
            setShowError(true)
            toast.info(t('error'))
        }
    }

    return (
        <div className="flex flex-col gap-4">
            <div className="w-full flex flex-col md:flex-row gap-2">
                <div className="w-full flex flex-col lg:flex-row gap-2">
                    <div className="w-1/2">
                        <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                            {t2('subject.name')}{' '}
                        </div>
                        <Select
                            value={formData.domainName}
                            onValueChange={value => {
                                handleFormChange('domainName', value)
                                handleFormChange('chapterId', '') // Reset chapterId when domain changes
                                handleFormChange('partIds', '')
                                setSelectedChapter('') // Reset selectedChapter state
                                const selectedDomain = domains.find(
                                    domain => domain.name === value
                                )
                                if (selectedDomain) {
                                    Cookies.set(
                                        'topicId',
                                        selectedDomain.id.toString()
                                    )
                                    Cookies.set('topic', selectedDomain.name)
                                }
                            }}
                        >
                            <SelectTrigger className="max-w-full">
                                <SelectValue
                                    placeholder={t2('subject.placeholder')}
                                />
                            </SelectTrigger>
                            <SelectContent
                                className={`${domains.length > 5 ? 'h-48' : 'h-fit'}`}
                            >
                                <SelectGroup>
                                    {domains.map(domain => (
                                        <SelectItem
                                            key={domain.id}
                                            value={domain.name}
                                        >
                                            {getLangProps({
                                                obj: domain,
                                                base: 'name',
                                                lang: locale
                                            })}
                                        </SelectItem>
                                    ))}
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="w-1/2">
                        <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                            {t('chap')}{' '}
                            {(formData.chapterId.length <= 0 && !showError) ||
                            formData.chapterId.length > 0 ? (
                                <InfoTooltip message={t('chois')} />
                            ) : (
                                <ErrorTooltip message={t('error-chois')} />
                            )}
                        </div>
                        <Select
                            value={formData.chapterId}
                            onValueChange={value => {
                                handleFormChange('partIds', '')
                                setSelectedChapter(value)
                            }}
                            disabled={
                                chapters.length <= 0 || domains.length <= 0 || !formData.domainName
                            }
                        >
                            <SelectTrigger className=" max-w-full">
                                <SelectValue placeholder={t('chois')} />
                            </SelectTrigger>
                            <SelectContent
                                className={`${chapters.length > 5 ? 'h-48' : 'h-fit'}`}
                            >
                                <SelectGroup>
                                    {chapters.map(chapter => (
                                        <SelectItem
                                            key={chapter.id}
                                            value={chapter.id}
                                        >
                                            {locale === 'ar'
                                                ? chapter.title_ar
                                                : locale === 'en'
                                                  ? chapter.title_en
                                                  : chapter.title}
                                        </SelectItem>
                                    ))}
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            </div>
            <div className="w-full flex flex-col md:flex-row gap-2">
                <div className="w-full sm:w-4/5 md:w-1/2">
                    <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('exonumber')}
                    </div>
                    <Input
                        type="number"
                        value={formData.exoNbr}
                        min={1}
                        max={10}
                        className="h-9 rounded-lg"
                        onChange={e =>
                            handleFormChange(
                                'exoNbr',
                                parseInt(e.target.value) || 1
                            )
                        }
                    />
                </div>
                <div className="w-full sm:w-4/5 md:w-1/2">
                    <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('questions-number')}
                    </div>
                    <Input
                        type="number"
                        value={formData.qstNbr}
                        min={1}
                        max={10}
                        className="h-9 rounded-lg"
                        onChange={e =>
                            handleFormChange(
                                'qstNbr',
                                parseInt(e.target.value) || 1
                            )
                        }
                    />
                </div>
            </div>
            <div className="w-full">
                <div className="w-full">
                    <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('part')}{' '}
                        {(formData.partIds?.length <= 0 && !showError) ||
                        formData.partIds?.length > 0 ? (
                            <InfoTooltip message={t('chois-part')} />
                        ) : (
                            <ErrorTooltip message={t('error-chois-part')} />
                        )}
                    </div>
                    <MultiSelect
                        options={parts.map(part => ({
                            label: getLangProps({
                                obj: part,
                                base: 'name',
                                lang: locale
                            }),
                            value: part.id
                        }))}
                        onValueChange={values =>
                            handleFormChange('partIds', values)
                        }
                        defaultValue={formData.partIds}
                        disabled={parts.length <= 0 || chapters.length <= 0 || !formData.chapterId}
                        maxCount={2}
                        placeholder={t('chois-part')}
                        badgeclassName="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/80 text-white"
                        translations={{
                            selectAll: tMultiSelect('selectAll'),
                            search: tMultiSelect('search'),
                            clear: tMultiSelect('clear'),
                            noResults: tMultiSelect('noResults'),
                            close: tMultiSelect('close'),
                            more: tMultiSelect('more')
                        }}
                    />
                </div>
            </div>
            <div className="w-full flex flex-col md:flex-row gap-2">
                <div className="w-full sm:w-4/5 md:w-1/2">
                    <div className="flex gap-1 px-[3px] items-center justify-between text-sm font-bold text-dinoBotDarkGray">
                        {t('difficulty')} <span>{formData.difficulty}/3</span>
                    </div>
                    <div className="py-3 px-[3px]">
                        <Slider
                            defaultValue={[formData.difficulty]}
                            min={0}
                            max={3}
                            step={1}
                            className={'w-full '}
                            onValueChange={value =>
                                handleFormChange('difficulty', value[0])
                            }
                        />
                    </div>
                </div>
            </div>

            <div className="w-full flex justify-center items-center mt-6">
                <Button
                    className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
                    onClick={submit}
                    disabled={!parts.length}
                >
                    {t('submit')}
                </Button>
            </div>
        </div>
    )
}

export default ExoFromDbMin
