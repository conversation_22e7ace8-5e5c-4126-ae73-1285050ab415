import { create } from 'zustand';
import { StudentProfileFormData } from '../student-profile.types';
import { User } from '@dinobot/prisma/lib/generated/zod/modelSchema/UserSchema';

interface StudentProfileState {
  // User data
  user: Partial<User>;

  // UI state
  isEditingProfile: boolean;
  currentView: 'profile' | 'change-password';

  // Form data
  profileFormData: StudentProfileFormData;

  // Loading states
  isLoading: boolean;
}

interface StudentProfileActions {
  // User actions
  setUser: (user: Partial<User>) => void;

  // UI actions
  setIsEditingProfile: (isEditing: boolean) => void;
  setCurrentView: (view: 'profile' | 'change-password') => void;

  // Form actions
  setProfileFormData: (data: Partial<StudentProfileFormData>) => void;
  resetProfileForm: () => void;

  // Loading actions
  setIsLoading: (loading: boolean) => void;
}

type StudentProfileStore = StudentProfileState & StudentProfileActions;

const defaultUser: Partial<User> = {
  id: '',
  email: '',
  firstName: '',
  lastName: '',
  gender: '',
  type: 'student',
  isAdmin: false
};

export const useStudentProfileStore = create<StudentProfileStore>((set, get) => ({
  // Initial state
  user: defaultUser,
  isEditingProfile: false,
  currentView: 'profile',
  profileFormData: {
    firstName: '',
    lastName: '',
    gender: ''
  },
  isLoading: false,

  // Actions
  setUser: (user: Partial<User>) => set({ user }),

  setIsEditingProfile: (isEditing: boolean) => set({ isEditingProfile: isEditing }),

  setCurrentView: (view: 'profile' | 'change-password') => set({ currentView: view }),

  setProfileFormData: (data: Partial<StudentProfileFormData>) =>
    set((state) => ({
      profileFormData: { ...state.profileFormData, ...data }
    })),

  resetProfileForm: () => {
    const { user } = get();
    set({
      profileFormData: {
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        gender: user.gender || ''
      }
    });
  },

  setIsLoading: (loading: boolean) => set({ isLoading: loading })
}));