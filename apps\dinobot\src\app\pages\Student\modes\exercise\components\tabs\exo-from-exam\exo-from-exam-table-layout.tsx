import { ColumnDef } from '@tanstack/react-table'
import React from 'react'
import ExoFromExamTable from './exo-from-exam-table'
import { ExoOutput } from '../../../trainig.types'
import { useTranslation } from 'react-i18next'

interface ExoFromExamTableLayoutProps {
    exams: ExoOutput[]
}

function ExoFromExamTableLayout({ exams }: ExoFromExamTableLayoutProps) {
    const {t} = useTranslation(['app/mode'],{keyPrefix:"exo.tab.exam.table"})
    const columns: ColumnDef<ExoOutput>[] = [
        {
            accessorKey: 'id'
        },
        {
            accessorFn: exo => exo.title,
            header: t('title'),
            id: 'exo.title',
            cell: ({ row }) => {
                const title = row.getValue('exo.title')
                return (
                    <div>
                        {title ? (
                            `${title}`
                        ) : (
                            <span className="italic text-dinoBotRed">
                                {t('nofill')}
                            </span>
                        )}
                    </div>
                )
            }
        },
        // {
        //   accessorFn: exo => exo.exam.title,
        //   header: "Titre d'examen",
        //   id:"exam.title",
        //   cell: ({row}) => {
        //     const title = row.getValue("exam.title")
        //     return(
        //       <div>
        //         {title ? `${title}` : <span className="italic text-dinoBotRed">Non renseigné</span>}
        //       </div>
        //     )
        //   },
        // },
        {
            accessorFn: exo => exo.exam.year,
            header: t('year'),
            id: 'exam.year'
        }
    ]

    return (
        <div>
            <ExoFromExamTable columns={columns} data={exams} />
        </div>
    )
}

export default ExoFromExamTableLayout
