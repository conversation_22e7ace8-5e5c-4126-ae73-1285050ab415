import React from 'react'

import { useQuery } from '@tanstack/react-query'

import { Theme } from '@dinobot/prisma'
import CourseDetailsCard from './course-details-card'
import ThemeBlock from './theme-bloc'
import { Accordion, ScrollArea } from '@dinobot/components-ui'
import { useAuthApiClient } from '../../../../../contexts/AppContext'

type AboutCourseProps = {
    courseId?: string
}

function AboutCourse({ courseId }: AboutCourseProps) {
    const apiClient = useAuthApiClient()

    const { data: themes, isLoading, error } = useQuery({
        queryKey: ['themes', courseId],
        queryFn: async (): Promise<Theme[]> => {
            if (!courseId) return []
            const response = await apiClient.get(`/api/themes/class/${courseId}`)
            return response.data
        },
        enabled: !!courseId
    })

    if (isLoading) {
        return <div className="flex items-center justify-center h-64">Loading themes...</div>
    }

    if (error) {
        return <div className="flex items-center justify-center h-64">Error loading themes</div>
    }

    return (
        <div className="flex flex-col gap-2 w-full h-[calc(100vh-3.5rem)]">
            <CourseDetailsCard id={courseId} />
            {/* {themes && themes?.length > 0 &&
        <div className='mx-2 w-full flex justify-end gap-2'>
        <IconButton icon={<Download />} rounded='rounded-sm' height='h-8' width='w-8' className='bg-dinoBotLightGray text-dinoBotBlackBlue hover:bg-dinoBotLightGray' onClick={()=>{
        }} />
        <IconButton icon={<Printer />} rounded='rounded-sm' height='h-8' width='w-8' className='bg-dinoBotLightGray text-dinoBotBlackBlue hover:bg-dinoBotLightGray' />
      </div> } */}
            <ScrollArea className="flex flex-col gap-2 ml-2 w-full h-[38%] pr-3">
                <Accordion
                    type="single"
                    collapsible
                    className="flex flex-col gap-2 w-full"
                >
                    {themes?.map(theme => (
                        <ThemeBlock key={theme.id} theme={theme} />
                    ))}
                </Accordion>
            </ScrollArea>
        </div>
    )
}

export default AboutCourse
