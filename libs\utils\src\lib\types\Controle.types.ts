import { ControlSchema, ControlScoringTypeSchema, ControlStatusSchema, ControlWithRelationsSchema, ExerciseMediaTypeSchema } from '@dinobot/prisma';
import { z } from 'zod';

export type Control = z.infer<typeof ControlWithRelationsSchema>;

// Define Desmos types locally
// export
 const DesmosSchema = z.object({
    expressions: z.any().optional(),
    graph: z.any().optional().describe('Graph settings')
})

type DesmosType = z.infer<typeof DesmosSchema>

export const ControlRowSchema = ControlSchema.extend({
  exerciseCount: z.number().int().positive().optional(),
  domainName: z.string().optional(),
  className: z.string().optional(),
});

export type ControlRow = z.infer<typeof ControlRowSchema> & Control;

export type ControlList = ControlRow[];

export const ControlQuestionMediaSchema = z.object({
  id: z.string().uuid().optional(),
  fileName: z.string().nullable(),
  fileUrl: z.string().nullable(),
  signedUrl: z.string().nullable(),
  fileType: z.string().nullable(),
  data: z.instanceof(Buffer).nullable(),
  type: ExerciseMediaTypeSchema,
  questionId: z.string().uuid(),
  createdAt: z.date(),
  updatedAt: z.date().nullable(),
});

const questionSchema = z.object({
  id: z.string(),
  questionContent: z.string(),
  answer: z.string().optional(),
  saved: z
    .boolean()
    .optional()
    .describe(
      'Ignore this field, it is used to check whether the answer is saved or not',
    ),
  feedback: z.string().optional(),
  desmosCode: DesmosSchema.optional(),
  contentType: z.enum(['text', 'html', 'latex']),
  medias: z.array(ControlQuestionMediaSchema),
});

const exerciseSchema = z.object({
  id: z.string(),
  title: z.string(),
  score: z.number().optional(),
  questions: z.array(questionSchema),
  medias: z.array(ControlQuestionMediaSchema),
  hasStatment: z.boolean(),
  statement: z.string(),
});

export const feedbackOutputSchema = z.object({
  score: z.number().optional(),
  scoreExplanation: z.string().optional(),
  exercises: z.array(exerciseSchema),
});

export type ControlFeedbackOutput = z.infer<typeof feedbackOutputSchema>;

export type ControlWithExercises = {
  id?: string;
  name: string;
  description?: string;
  hasSolution?: boolean;
  isTimed?: boolean;
  scoringType: z.infer<typeof ControlScoringTypeSchema>;
  status: z.infer<typeof ControlStatusSchema>;
  domainName?: string;
  levelName?: string;
  numericMaxScore?: number;
  letterRange?: string;
  criteriaChoices?: string;
  authorId?: string;
  assignedClassId?: string;
  createdAt?: Date;
  updatedAt?: Date;
  exercises: ExercisesWithQuestions[];
};

export type ExercisesWithQuestions = {
  id?: string;
  title: string;
  description?: string;
  partId?: string;
  controlId?: string;
  createdAt?: Date;
  updatedAt?: Date;
  questions: QuestionWithMedias[];
  medias?: ControlQuestionMedia[];
};

export type QuestionWithMedias = {
  id?: string;
  content?: string;
  solution?: string;
  type?: string;
  desmosCode?: z.infer<typeof DesmosSchema>;
  controlExerciseId?: string;
  createdAt?: Date;
  updatedAt?: Date;
  medias?: ControlQuestionMedia[];
};

export type ControlQuestionMedia = {
  id?: string;
  fileName: string | null;
  fileUrl: string | null;
  signedUrl: string | null;
  fileType: string | null;
  data: Buffer | null;
  type: z.infer<typeof ExerciseMediaTypeSchema>;
  questionId: string;
  createdAt: Date;
  updatedAt: Date | null;
};

export type InputControl = Omit<
  ControlWithExercises,
  'id' | 'createdAt' | 'updatedAt'
>;

export type UpdateInputControl = Omit<
  ControlWithExercises,
  'createdAt' | 'updatedAt'
>;

export interface ControleModeGeneratorOutput {
    [key: string]: any
}

export interface TrainingModeGeneratorOutput {
    [key: string]: any
}



export interface ControlPartialWithRelationsSchema {
    exercises?: {
        questions?: {
            id: string
            content: string
            solution?: string
            desmosCode?: any
            type?: string
        }[]
    }[]
}
