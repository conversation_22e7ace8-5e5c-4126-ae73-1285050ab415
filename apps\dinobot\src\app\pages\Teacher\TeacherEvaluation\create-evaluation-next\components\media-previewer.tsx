import { Label } from '@dinobot/components-ui'
import { ControlQuestionMedia } from '@prisma/client'
import { Minus, Plus } from 'lucide-react'
import React, { useState } from 'react'
import Cropper from 'react-easy-crop'

type MediaPreviewerProps = {
    media: ControlQuestionMedia
    setCroppedPixels: (croppedPixels: any) => void
}

function MediaPreviewer({ media, setCroppedPixels }: MediaPreviewerProps) {
    const [crop, setCrop] = useState({ x: 0, y: 0 })
    const [zoom, setZoom] = useState(1)

    const onCropComplete = (_croppedArea: any, croppedAreaPixels: any) => {
        setCroppedPixels(croppedAreaPixels)
    }

    return (
        <div>
            <div className="relative rounded-md overflow-hidden bg-dinoBotDarkGray border size-80">
                {media?.fileType?.startsWith('image') ? (
                    <>
                        <Cropper
                            classes={{
                                containerClassName: 'w-full h-full'
                            }}
                            image={media.fileUrl!}
                            crop={crop}
                            zoom={zoom}
                            aspect={4 / 3}
                            onCropChange={setCrop}
                            onCropComplete={onCropComplete}
                            onZoomChange={setZoom}
                        />
                    </>
                ) : (
                    <video
                        src={media.fileUrl!}
                        controls
                        muted={false}
                        className="object-cover w-full h-full"
                    />
                )}
            </div>

            {media?.fileType?.startsWith('image') && (
                <div className="flex justify-center items-center gap-2 h-12">
                    <button
                        onClick={() => {
                            setZoom(prevZoom => prevZoom - 0.1)
                        }}
                        className=" px-4 py-2 bg-blue-600 text-white rounded-full"
                        onMouseDown={e => {
                            e.preventDefault()
                            const interval = setInterval(() => {
                                setZoom(prevZoom => {
                                    if (prevZoom <= 0.1) return prevZoom
                                    return prevZoom - 0.1
                                })
                            }, 150)
                            const onMouseUp = () => {
                                clearInterval(interval)
                                window.removeEventListener('mouseup', onMouseUp)
                            }
                            window.addEventListener('mouseup', onMouseUp)
                        }}
                    >
                        <Minus size={20} />
                    </button>

                    <Label className="h-full flex justify-center items-center">
                        {Math.round(zoom * 100)}%
                    </Label>

                    <button
                        onClick={() => {
                            setZoom(prevZoom => prevZoom + 0.1)
                        }}
                        onMouseDown={e => {
                            e.preventDefault()
                            const interval = setInterval(() => {
                                setZoom(prevZoom => prevZoom + 0.1)
                            }, 150)
                            const onMouseUp = () => {
                                clearInterval(interval)
                                window.removeEventListener('mouseup', onMouseUp)
                            }
                            window.addEventListener('mouseup', onMouseUp)
                        }}
                        className="px-4 py-2 bg-blue-600 text-white rounded-full"
                    >
                        <Plus size={20} />
                    </button>
                </div>
            )}
        </div>
    )
}

export default MediaPreviewer
