{"info": {"name": "DinoBot Control Mode API Collection", "description": "API collection for DinoBot Control Mode endpoints - evaluation, control generation, and management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3001", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}, {"key": "controlId", "value": "", "type": "string"}, {"key": "classId", "value": "", "type": "string"}, {"key": "themeId", "value": "", "type": "string"}, {"key": "plannedEvaluationId", "value": "", "type": "string"}, {"key": "levelId", "value": "", "type": "string"}, {"key": "domainId", "value": "", "type": "string"}], "auth": {"type": "<PERSON><PERSON><PERSON>"}, "item": [{"name": "Prerequisites", "item": [{"name": "Login to get access token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should have success true\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "// Extract tokens from cookies for future requests", "var cookies = pm.cookies.all();", "var tokenCookie = cookies.find(cookie => cookie.name === 'token');", "", "if (tokenC<PERSON>ie) {", "    pm.collectionVariables.set('accessToken', tokenCookie.value);", "    console.log('Access token stored:', tokenCookie.value);", "} else {", "    console.log('No access token found in cookies');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"remember\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}, "response": []}]}, {"name": "Control Generator", "item": [{"name": "Generate Questions", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should contain generated questions\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"levelId\": \"{{levelId}}\",\n  \"domainId\": \"{{domainId}}\",\n  \"numberOfQuestions\": 5,\n  \"subject\": \"Mathématiques\",\n  \"difficulty\": \"medium\",\n  \"description\": \"Génération de questions sur les fractions\"\n}"}, "url": {"raw": "{{baseUrl}}/api/control-generator/questions?locale=fr", "host": ["{{baseUrl}}"], "path": ["api", "control-generator", "questions"], "query": [{"key": "locale", "value": "fr"}]}}, "response": []}, {"name": "Generate Control", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should contain generated control\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"levelId\": \"{{levelId}}\",\n  \"domainId\": \"{{domainId}}\",\n  \"subject\": \"Algèbre\",\n  \"numberOfQuestions\": 10,\n  \"difficulty\": \"hard\",\n  \"duration\": 60,\n  \"instructions\": \"Contrôle sur les équations du second degré\"\n}"}, "url": {"raw": "{{baseUrl}}/api/control-generator/controle?locale=fr", "host": ["{{baseUrl}}"], "path": ["api", "control-generator", "controle"], "query": [{"key": "locale", "value": "fr"}]}}, "response": []}, {"name": "Generate Control from File", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should contain generated control from file\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/your/document.pdf", "description": "PDF document to analyze"}, {"key": "solutionFile", "type": "file", "src": "/path/to/solution.pdf", "description": "Optional solution file"}, {"key": "numberOfQuestions", "value": "8", "type": "text"}, {"key": "difficulty", "value": "medium", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/control-generator/controle-by-file?locale=fr", "host": ["{{baseUrl}}"], "path": ["api", "control-generator", "controle-by-file"], "query": [{"key": "locale", "value": "fr"}]}}, "response": []}, {"name": "Generate Feedback", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should contain AI feedback\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"questionId\": \"question-id-here\",\n  \"studentAnswer\": \"La réponse de l'étudiant ici\",\n  \"correctAnswer\": \"La réponse correcte ici\",\n  \"context\": \"Contexte de la question\"\n}"}, "url": {"raw": "{{baseUrl}}/api/control-generator/feedback?locale=fr", "host": ["{{baseUrl}}"], "path": ["api", "control-generator", "feedback"], "query": [{"key": "locale", "value": "fr"}]}}, "response": []}]}, {"name": "Control Management", "item": [{"name": "Get All Controls", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should be an array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "// Store first control ID if available", "var jsonData = pm.response.json();", "if (jsonData.length > 0) {", "    pm.collectionVariables.set('controlId', jsonData[0].id);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", ""]}}, "response": []}, {"name": "Get Control by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should contain control data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.id).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/{{controlId}}", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "{{controlId}}"]}}, "response": []}, {"name": "Get Control with Media", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should contain control with media\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.id).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/{{controlId}}/media", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "{{controlId}}", "media"]}}, "response": []}, {"name": "Create Control", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201 or 200\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test(\"Response should contain created control\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.id).to.exist;", "    pm.collectionVariables.set('controlId', jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Contrôle Mathématiques\",\n  \"description\": \"Évaluation sur les fonctions\",\n  \"duration\": 90,\n  \"levelId\": \"{{levelId}}\",\n  \"domainId\": \"{{domainId}}\",\n  \"questions\": [\n    {\n      \"content\": \"Résoudre l'équation x² + 2x - 8 = 0\",\n      \"type\": \"open\",\n      \"points\": 5\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/control-mode/", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", ""]}}, "response": []}, {"name": "Update Control", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should contain updated control\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.id).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Contrôle Mathématiques - Mis à jour\",\n  \"description\": \"Évaluation modifiée sur les fonctions\",\n  \"duration\": 120\n}"}, "url": {"raw": "{{baseUrl}}/api/control-mode/{{controlId}}", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "{{controlId}}"]}}, "response": []}]}, {"name": "Planned Evaluations", "item": [{"name": "Get Planned Controls by Theme", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should be an array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/planned/theme/{{themeId}}", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "planned", "theme", "{{themeId}}"]}}, "response": []}, {"name": "Get Planned Controls by Class", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should be an array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "// Store first planned evaluation ID if available", "var jsonData = pm.response.json();", "if (jsonData.length > 0) {", "    pm.collectionVariables.set('plannedEvaluationId', jsonData[0].id);", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/planned/class/{{classId}}", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "planned", "class", "{{classId}}"]}}, "response": []}, {"name": "Get Planned Evaluation by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should contain planned evaluation data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.id).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/planned/{{plannedEvaluationId}}", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "planned", "{{plannedEvaluationId}}"]}}, "response": []}]}, {"name": "Student Submissions", "item": [{"name": "Get Submissions by Control", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should be an array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/submissions/{{controlId}}", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "submissions", "{{controlId}}"]}}, "response": []}, {"name": "Get Student Submissions by Planned Evaluation", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should be an array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/student-submissions/{{plannedEvaluationId}}", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "student-submissions", "{{plannedEvaluationId}}"]}}, "response": []}, {"name": "Get Submissions Count", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should contain count\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.count).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/student-submissions/{{plannedEvaluationId}}/count", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "student-submissions", "{{plannedEvaluationId}}", "count"]}}, "response": []}, {"name": "Create Student Submission", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201 or 200\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test(\"Response should contain submission data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"answers\": [\n    {\n      \"questionId\": \"question-1\",\n      \"answer\": \"x = 2 ou x = -4\",\n      \"confidence\": 8\n    },\n    {\n      \"questionId\": \"question-2\",\n      \"answer\": \"La dérivée est f'(x) = 2x + 1\",\n      \"confidence\": 9\n    }\n  ],\n  \"submissionTime\": \"2024-01-15T10:30:00Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/control-mode/student-submissions/{{plannedEvaluationId}}", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "student-submissions", "{{plannedEvaluationId}}"]}}, "response": []}]}, {"name": "Controls by Context", "item": [{"name": "Get Controls by Theme", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should be an array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/theme/{{themeId}}", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "theme", "{{themeId}}"]}}, "response": []}, {"name": "Get Controls by Class", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should be an array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/class/{{classId}}", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "class", "{{classId}}"]}}, "response": []}, {"name": "Get Unassigned Controls by Class", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should be an array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/class/{{classId}}/unassigned", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "class", "{{classId}}", "unassigned"]}}, "response": []}]}, {"name": "Evaluation & Comments", "item": [{"name": "Get Evaluation by Planned ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should contain evaluation data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/evaluation/{{plannedEvaluationId}}", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "evaluation", "{{plannedEvaluationId}}"]}}, "response": []}, {"name": "Get Professor Comments", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response should contain comments\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "<PERSON><PERSON>", "value": "token={{accessToken}}"}], "url": {"raw": "{{baseUrl}}/api/control-mode/evaluation/{{plannedEvaluationId}}/comments", "host": ["{{baseUrl}}"], "path": ["api", "control-mode", "evaluation", "{{plannedEvaluationId}}", "comments"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Pre-request script for the entire collection", "// This runs before every request in the collection", "", "// Set default headers if needed", "pm.request.headers.add({", "    key: 'Accept',", "    value: 'application/json'", "});"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Test script for the entire collection", "// This runs after every request in the collection", "", "pm.test(\"Response time is less than 5000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test(\"Response should be JSON\", function () {", "    pm.response.to.be.json;", "});"]}}]}