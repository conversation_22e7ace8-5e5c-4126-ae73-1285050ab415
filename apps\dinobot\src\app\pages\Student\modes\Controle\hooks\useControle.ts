import { useMutation, useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../../contexts/AppContext';

export interface ControlModeGeneratorInput {
  chapterId: string
  domain: string
}

export interface ControlFeedbackOutput {
  [key: string]: any
}

export const useGenerateControle = () => {
  const apiClient = useAuthApiClient()

  return useMutation({
    mutationFn: async (data: ControlModeGeneratorInput) => {
      const response = await apiClient.post('/api/control-generator/controle', data)
      return response.data
    }
  })
}

export const useGenerateFeedback = () => {
  const apiClient = useAuthApiClient()

  return useMutation({
    mutationFn: async (controleData: ControlFeedbackOutput) => {
      const response = await apiClient.post('/api/control-generator/feedback', controleData)
      return response.data
    }
  })
}

export const useDomain = (domainId: number | null) => {
  const apiClient = useAuthApiClient()

  return useQuery({
    queryKey: ['domain', domainId],
    queryFn: async () => {
      if (!domainId) return null
      const response = await apiClient.get(`/api/domains/${domainId}`)
      return response.data
    },
    enabled: !!domainId && !!apiClient
  })
}
