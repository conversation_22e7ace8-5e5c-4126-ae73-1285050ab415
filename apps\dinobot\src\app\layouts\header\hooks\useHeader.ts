import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../contexts/AppContext'
import { Domain } from '@prisma/client'
import { selectAccountStore } from '@dinobot/stores/lib/account-store/store'

export const useHeader = () => {
    const authClient = useAuthApiClient()
    const user = selectAccountStore.use.user()

    const { data: domains = [], isLoading: domainsLoading, error: domainsError } = useQuery<Domain[]>({
        queryKey: ['domains', user?.levelId],
        queryFn: async () => {
            if (!user?.levelId) return []
            const response = await authClient.get(`/api/domain-level/domains/${user.levelId}`)
            return response.data
        },
        enabled: !!user?.levelId
    })

    return {
        domains,
        domainsLoading,
        domainsError
    }
}