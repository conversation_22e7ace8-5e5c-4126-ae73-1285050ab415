# Node modules (will be installed during build)
node_modules/
*/node_modules/
**/node_modules/

# Build outputs (will be created during build)
dist/
*/dist/
**/dist/
build/
*/build/
**/build/

# Development files
.env
.env.local
.env.*.local
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Testing
coverage/
.nyc_output
*.tsbuildinfo

# Cypress
cypress/videos/
cypress/screenshots/

# Temporary files
tmp/
temp/
*.tmp

# Docker
Dockerfile*
.dockerignore
docker-compose*.yml

# Development tools
.eslintcache
.cache/

# Nx cache
.nx/

# Storybook
storybook-static/

# Documentation
*.md
!README.md

# Postman collections (not needed in production)
postman_collections/

# E2E test artifacts (not needed in production)
apps/dinobot-e2e/

# Local development scripts
scripts/dev/