import Chat from './components/Chat'
import { Session } from '@dinobot/utils'
import { useNavigate, useParams } from 'react-router-dom'
import Cookies from 'js-cookie'
import { useFeatureRoutes } from '../../../hooks/useFeatureFlags'
import { useEffect } from 'react'
import { useChat } from './hooks/useChat'

// TODO title metadata
// export async function generateMetadata(
//     props: ChatPageProps
// ): Promise<Metadata> {
//     const params = await props.params
//     const session = await auth()

//     if (!session?.user) {
//         return {}
//     }

//     const chat = await getChat(params.id, session.user.id)
//     return {
//         title: chat?.title.toString().slice(0, 50) ?? 'Chat'
//     }
// }

const ChatById = () => {
    const params = useParams()
    const session = JSON.parse(Cookies.get('session') || '{}') as Session
    const navigate = useNavigate()
    const { data: featureFlags } = useFeatureRoutes('STUDENT_CHAT_MODE')
    const { getChatById } = useChat()

    useEffect(() => {
        if (featureFlags && Array.isArray(featureFlags) && featureFlags[0] != null) {
            navigate(`/${featureFlags[0]}`)
        }
    }, [featureFlags, navigate])

    useEffect(() => {
        if (!session?.user) {
            navigate(`/login?next=/chat/${params.id}`)
        }
    }, [session, params.id, navigate])

    useEffect(() => {
        const checkChatAccess = async () => {
            if (params.id && session?.user?.id) {
                const chat = await getChatById(params.id)
                if (chat && chat.userId !== session.user.id) {
                    navigate('/chat')
                }
            }
        }
        checkChatAccess()
    }, [params.id, session?.user?.id, getChatById, navigate])

    return (
        <Chat session={session} idp={params.id} />
    )
}

export default ChatById