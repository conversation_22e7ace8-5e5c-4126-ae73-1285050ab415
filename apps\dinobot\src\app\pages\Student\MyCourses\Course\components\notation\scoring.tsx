import React, { useEffect } from 'react'
import ScoringList from './scoring-list'
import ScoringDetails from './scoring-details'
import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import { selectorNotationStore } from '../../../stores/notation.store'
import { Separator } from '@dinobot/components-ui'
import { useNotation } from '../../../hooks/useNotation'

const Scoring = () => {
    const { courseId } = useParams<{ courseId: string }>()
    const { i18n } = useTranslation()
    const dir = getLangDir(i18n.language)
    const setPlannedEvaluations = selectorNotationStore.use.setPlannedEvaluations()
    
    const { data: notationData, isLoading, error } = useNotation(courseId)

    useEffect(() => {
        if (notationData) {
            setPlannedEvaluations(notationData)
        }
    }, [notationData, setPlannedEvaluations])

    return (
        <div className="flex h-full" dir={dir}>
            <ScoringList />
            <Separator orientation="vertical" />
            <ScoringDetails />
        </div>
    )
}

export default Scoring
