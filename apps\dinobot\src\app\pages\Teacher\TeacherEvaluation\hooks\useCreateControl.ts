import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { ControlPartialWithRelations, DomainPartial, LevelPartial } from '@dinobot/prisma'

interface CreateControlParams {
    data: ControlPartialWithRelations
    level: LevelPartial
    domain: DomainPartial
}

export const useCreateControl = () => {
    const apiClient = useAuthApiClient()
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: async (params: CreateControlParams): Promise<ControlPartialWithRelations> => {
            if (!apiClient) throw new Error('API client not available')
            
            const response = await apiClient.post('/api/control-mode/', params)
            return response.data
        },
        onSuccess: () => {
            // Invalidate and refetch controls queries
            queryClient.invalidateQueries({ queryKey: ['controls'] })
            queryClient.invalidateQueries({ queryKey: ['controls-by-class'] })
        }
    })
}