import React, { useState, useEffect } from 'react'
import ScoringCard from './scoring-card'
import { selectorNotationStore } from '../../../stores/notation.store'
import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { ScrollArea, Toggle } from '@dinobot/components-ui'
import { useNotation } from '../../../hooks/useNotation'

const ScoringList = () => {
    const { courseId } = useParams<{ courseId: string }>()
    const { t } = useTranslation(['app/courses/notation'])
    const [order, setOrder] = useState<'asc' | undefined>(undefined)
    const plannedEvaluations = selectorNotationStore.use.plannedEvaluations()
    const setPlannedEvaluations = selectorNotationStore.use.setPlannedEvaluations()
    
    const { data: notationData } = useNotation(courseId, order)
    
    useEffect(() => {
        if (notationData) {
            setPlannedEvaluations(notationData)
        }
    }, [notationData, setPlannedEvaluations])
    
    return (
        <div className="flex-[2] h-full ">
            <Toggle
                className="p-2 h-7 border data-[state=on]:bg-dinoBotBlue text-dinoBotDarkGray data-[state=on]:text-dinoBotWhite"
                onPressedChange={is => setOrder(is ? 'asc' : undefined)}
            >
                {t('toggle_order')}
            </Toggle>
            <ScrollArea className="h-[calc(100%-75px)]">
                <div className="flex flex-col gap-4 p-3">
                    {plannedEvaluations.map(evaluation => (
                        <ScoringCard
                            key={evaluation.id}
                            plannedEvaluation={evaluation}
                        />
                    ))}
                </div>
            </ScrollArea>
        </div>
    )
}

export default ScoringList
