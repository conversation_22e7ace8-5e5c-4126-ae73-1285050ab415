import { Button } from '@dinobot/components-ui'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>Content,
    <PERSON><PERSON>Footer,
    <PERSON><PERSON>Header,
    <PERSON><PERSON>Title,
    DialogTrigger
} from '@dinobot/components-ui'
import { Image as ImportIcon, PlusIcon, Video } from 'lucide-react'
import React, { useState } from 'react'
import { useEvaluationParamsStore } from '../../store/evaluation-params.store'
import {
    ControlQuestionPartialWithRelations,
    ControlExerciseMedia,
    ControlQuestionMedia,
    ControlQuestionMediaPartial,
    ControlQuestionMediaPartialWithRelations,
    ControlExercisePartialRelations
} from '@dinobot/prisma'
import MediaPreviewer from './media-previewer'
import { toast } from 'sonner'
import { cn } from '@dinobot/utils'
import { useTranslation } from 'react-i18next'
import { ExerciseMediaType } from '@prisma/client'

type MediaDialogProps = {
    dialogType: ExerciseMediaType
    quesNum?: number
    exoIndex?: number
    question?: ControlQuestionPartialWithRelations
    exercise?: ControlExercisePartialRelations
    questionMedias?: ControlQuestionMedia[]
    exercisesMedia?: ControlExerciseMedia[]
    setQuestionMedias?: (questionMedias: ControlQuestionMedia[]) => void
    setExercisesMedia?: (exerciseMedias: ControlExerciseMedia[]) => void
    className?: string
}

// Allowed MIME types for file uploads (images and videos)
const ALLOWED_MIME_TYPES = [
    'image/jpeg',
    'image/png',
    'image/jpg',
    'image/gif',
    'video/mp4',
    'video/webm'
]

// Maximum file size in MB
const MAX_FILE_SIZE = 10
// Maximum number of attachments allowed per exercise/question
const MAX_FILES_COUNT = 10

/**
 * MediaDialog Component
 *
 * Provides a dialog for uploading and managing media files (images and videos)
 * for exercises and questions in the evaluation creation process.
 *
 * Features:
 * - File type validation
 * - File size validation
 * - Image cropping
 * - Internationalization support
 */
function MediaDialog({
    className,
    dialogType,
    quesNum,
    exoIndex,
    exercise,
    question,
    setQuestionMedias,
    questionMedias,
    exercisesMedia,
    setExercisesMedia
}: MediaDialogProps) {
    // State for the currently selected media file
    const [mediaState, setMedia] =
        useState<ControlQuestionMediaPartial | null>()
    // State for image cropping coordinates
    const [croppedPixels, setCroppedPixels] = useState<any>(null)
    // Get evaluation parameters from store
    const { updateQuestion, updateExo, setMediaCount, mediaCount } =
        useEvaluationParamsStore()
    // Initialize translations
    const { t } = useTranslation('teacher/myClass/evaluation')

    /**
     * Crops an image based on the provided coordinates
     * @param imageSrc - Source URL of the image to crop
     * @param pixelCrop - Coordinates and dimensions for cropping
     * @returns Promise resolving to a Blob of the cropped image
     */
    const getCroppedImg = async (
        imageSrc: string,
        pixelCrop: { x: number; y: number; width: number; height: number }
    ): Promise<Blob> => {
        const createImage = (url: string): Promise<HTMLImageElement> =>
            new Promise((resolve, reject) => {
                const img = new Image()
                img.crossOrigin = 'anonymous'
                img.onload = () => resolve(img)
                img.onerror = e => reject(e)
                img.src = url
            })

        const image = await createImage(imageSrc)
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')

        if (!ctx) throw new Error('Could not get canvas context')

        canvas.width = pixelCrop.width
        canvas.height = pixelCrop.height

        ctx.drawImage(
            image,
            pixelCrop.x,
            pixelCrop.y,
            pixelCrop.width,
            pixelCrop.height,
            0,
            0,
            pixelCrop.width,
            pixelCrop.height
        )

        return new Promise((resolve, reject) => {
            canvas.toBlob(blob => {
                if (!blob) {
                    reject(new Error('Canvas is empty'))
                    return
                }
                resolve(blob)
            }, 'image/jpeg')
        })
    }

    /**
     * Handles saving media based on the media type
     * @param type - Type of media (question, statement, or global)
     */
    async function handleSave(type: ExerciseMediaType) {
        if (type == 'question') {
            await handleQuestionSave()
        } else if (type == 'statement' || type == 'global') {
            await handleExerciseSave()
        }
    }

    /**
     * Handles saving media for exercises
     * Validates file size limits and processes images for cropping if needed
     */
    const handleExerciseSave = async () => {
        console.log('handle exercise save', mediaCount)
        if (mediaCount >= MAX_FILES_COUNT) {
            toast.error(
                t('mediaDialog.maxAttachmentsReached', {
                    maxCount: MAX_FILES_COUNT
                })
            )
            return
        }
        let cropedImage
        if (mediaState?.fileType?.includes('image'))
            cropedImage = await handleCropAndSave()
        if (setExercisesMedia && mediaState)
            setExercisesMedia([
                ...(exercisesMedia ?? []),
                {
                    createdAt: new Date(),
                    updatedAt: null,
                    fileName: mediaState?.fileName,
                    fileType: mediaState?.fileType,
                    data: cropedImage?.file,
                    fileUrl: mediaState?.fileType?.includes('image')
                        ? cropedImage?.urlPreview
                        : mediaState?.fileUrl,
                    type: dialogType as ExerciseMediaType
                }
            ] as ControlExerciseMedia[])
        const { questionId, ...media } = mediaState || {}
        media.type = dialogType
        if (mediaState?.fileType?.includes('image')) {
            media.fileUrl = cropedImage?.urlPreview
            media.data = cropedImage?.file as Buffer
        } else {
            media.fileUrl = mediaState?.fileUrl
            media.data = mediaState?.data
        }
        updateExo(exoIndex!, {
            ...exercise,
            medias: [...(exercise?.medias ?? []), media]
        })
        setMediaCount(mediaCount + 1)
        setMedia(null)
    }

    /**
     * Handles saving media for questions
     * Validates file size limits and processes images for cropping if needed
     */
    const handleQuestionSave = async () => {
        if (mediaCount >= MAX_FILES_COUNT) {
            toast.error(
                t('mediaDialog.maxAttachmentsReached', {
                    maxCount: MAX_FILES_COUNT
                })
            )
            return
        }
        let cropedImage
        if (mediaState?.fileType?.includes('image')) {
            cropedImage = await handleCropAndSave()
        }
        if (setQuestionMedias && mediaState)
            setQuestionMedias([
                ...(questionMedias ?? []),
                {
                    createdAt: new Date(),
                    updatedAt: null,
                    fileName: mediaState?.fileName,
                    fileType: mediaState?.fileType,
                    data: cropedImage?.file,
                    fileUrl: mediaState?.fileType?.includes('image')
                        ? cropedImage?.urlPreview
                        : mediaState?.fileUrl,
                    type: 'image' as ExerciseMediaType
                }
            ] as ControlQuestionMedia[])
        const { questionId, ...med } = mediaState || {}

        med.type = dialogType as ExerciseMediaType
        if (mediaState?.fileType?.includes('image')) {
            med.fileUrl = cropedImage?.urlPreview
            med.data = cropedImage?.file as Buffer
        } else {
            med.fileUrl = mediaState?.fileUrl
            med.data = mediaState?.data
        }
        updateQuestion(exoIndex!, quesNum!, {
            ...question,
            medias: [
                ...(questionMedias ?? []),
                med
            ] as ControlQuestionMediaPartialWithRelations[]
        })
        setMediaCount(mediaCount + 1)
        setMedia(null)
    }

    /**
     * Processes cropped images and converts them to the required format
     * Validates file size after cropping
     * @returns Object containing the preview URL and file data
     */
    const handleCropAndSave = async () => {
        if (!croppedPixels) return
        const blob = await getCroppedImg(
            mediaState?.fileUrl ?? '',
            croppedPixels
        )
        // blob to base64
        const base64 = await new Promise((resolve, reject) => {
            const reader = new FileReader()
            reader.onloadend = () => resolve(reader.result)
            reader.onerror = reject
            reader.readAsDataURL(blob)
        })

        const file = new File([blob], 'cropped.jpg', { type: 'image/jpeg' })
        //if file size > 5mb
        if (file.size > MAX_FILE_SIZE * 1024 * 1024) {
            toast.error(
                t('mediaDialog.fileTooLarge', {
                    fileName: file.name,
                    maxSize: MAX_FILE_SIZE
                })
            )
            return
        }

        return { urlPreview: await URL.createObjectURL(file), file: base64 }
    }

    /**
     * Handles file selection from the input element
     * Validates file type and size before processing
     * @param e - Input change event
     * @param type - Type of media (IMAGE or VIDEO)
     */
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0]
        if (!file) return

        const reader = new FileReader()

        reader.onload = () => {
            if (file.size > MAX_FILE_SIZE * 1024 * 1024) {
                toast.error(
                    t('mediaDialog.fileTooLarge', {
                        fileName: file.name,
                        maxSize: MAX_FILE_SIZE
                    })
                )
                return
            }
            if (!ALLOWED_MIME_TYPES.includes(file.type)) {
                toast.error(
                    t('mediaDialog.invalidFileType', { fileName: file.name })
                )
                return
            }

            setMedia({
                createdAt: new Date(),
                updatedAt: undefined,
                fileName: file.name,
                fileType: file.type,
                data: file as any,
                fileUrl: URL.createObjectURL(file),
                questionId: '',
                type: dialogType as ExerciseMediaType
            })
        }

        reader.readAsArrayBuffer(file)
    }

    // Render the dialog component with file upload options
    return (
        <Dialog>
            {/* Trigger button for opening the dialog */}
            <DialogTrigger asChild>
                <Button
                    variant="outline"
                    className={cn(
                        'max-w-52 border border-dinoBotBlue text-dinoBotBlue hover:text-dinoBotBlue',
                        className
                    )}
                >
                    {(questionMedias || exercisesMedia) &&
                    (questionMedias?.length === 0 ||
                        exercisesMedia?.length === 0) ? (
                        t('mediaDialog.addAttachment')
                    ) : (
                        <PlusIcon size={16} />
                    )}
                </Button>
            </DialogTrigger>
            <DialogContent className="min-h-[400px] h-fit max-w-[800px]">
                <DialogHeader>
                    <DialogTitle className="flex justify-center items-center p-2 text-dinoBotGray font-bold">
                        {t('mediaDialog.import')}
                    </DialogTitle>
                </DialogHeader>

                {/* Media upload section - shows either the media preview or upload options */}
                <section className="flex justify-center items-center border border-dinoBotGray p-2 gap-2">
                    {mediaState ? ( // If media is selected, show the preview
                        <MediaPreviewer
                            media={mediaState as ControlQuestionMedia}
                            setCroppedPixels={setCroppedPixels}
                        />
                    ) : (
                        <>
                            {/* Image upload option */}
                            <div className="flex justify-center items-center flex-col bg-dinoBotLightGray h-fit w-36 rounded overflow-hidden relative gap-2 py-4">
                                <input
                                    type="file"
                                    accept="image/*"
                                    className="opacity-0 w-full h-full absolute z-10 cursor-pointer"
                                    onChange={e => handleFileChange(e)}
                                />
                                <div className="flex justify-center items-center z-0 bg-dinoBotWhite rounded-full w-16 h-16">
                                    <ImportIcon className="text-dinoBotBlackBlue z-0" />
                                </div>
                                <p className="text-dinoBotBlackBlue text-center font-semibold">
                                    {t('mediaDialog.importImage')}
                                </p>
                            </div>

                            {/* Video upload option */}
                            <div className="flex justify-center items-center flex-col bg-dinoBotLightGray h-fit w-36 rounded overflow-hidden relative gap-2 py-4">
                                <input
                                    type="file"
                                    accept="video/*"
                                    className="opacity-0 w-full h-full absolute z-10 cursor-pointer"
                                    onChange={e => handleFileChange(e)}
                                />
                                <div className="flex justify-center items-center z-0 bg-dinoBotWhite rounded-full w-16 h-16">
                                    <Video className="text-dinoBotBlackBlue z-0" />
                                </div>
                                <p className="text-dinoBotBlackBlue text-center font-semibold">
                                    {t('mediaDialog.importVideo')}
                                </p>
                            </div>
                        </>
                    )}
                </section>

                {/* Dialog footer with action buttons */}
                <DialogFooter style={{ justifyContent: 'space-between' }}>
                    {/* Cancel button - only shown when media is selected */}
                    {mediaState && (
                        <Button
                            variant={'ghost'}
                            className="text-dinoBotRed underline hover:text-dinoBotRed hover:bg-"
                            onClick={() => setMedia(null)}
                        >
                            {t('mediaDialog.cancel')}
                        </Button>
                    )}
                    {mediaState && (
                        <Button
                            type="submit"
                            variant={'ghost'}
                            className="text-dinoBotBlue underline hover:text-dinoBotBlue hover:bg-"
                            onClick={() => handleSave(dialogType)}
                        >
                            {t('mediaDialog.save')}
                        </Button>
                    )}
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

export default MediaDialog
