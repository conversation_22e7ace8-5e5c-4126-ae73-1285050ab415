'use client'

import React, { useState } from 'react'
import { toast } from 'sonner'
import { IconSpinner ,Input,PasswordVisibility} from '@dinobot/components-ui'
import { useTranslation } from 'react-i18next'
import { useStudentProfile } from '../hooks/useStudentProfile'

export default function ChangePasswordForm() {
    const { changePassword, isChangingPassword } = useStudentProfile()
    const [showOldPassword, setShowOldPassword] = useState(false)
    const [showNewPassword, setShowNewPassword] = useState(false)
    const [showNewPasswordConfirmation, setShowNewPasswordConfirmation] = useState(false)
    const [passwords, setPasswords] = useState({
        oldPassword: '',
        newPassword: '',
        confirmNewPassword: ''
    })
    const {t} = useTranslation(['app/chat'], { keyPrefix: 'profile.change-password' })
    
    const handleShowPassword =
        (setShowPassword: (boolean: boolean) => void) =>
        (password: boolean) => {
            setShowPassword(!password)
        }
    
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setPasswords(prev => ({ ...prev, [name]: value }))
    }
    
    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        
        if (passwords.newPassword !== passwords.confirmNewPassword) {
            toast.error(t('passwords-not-match'))
            return
        }
        
        try {
            const result = await changePassword(passwords)
            if (result.type === 'success') {
                toast.success(result.message || t('password-updated'))
                setPasswords({
                    oldPassword: '',
                    newPassword: '',
                    confirmNewPassword: ''
                })
            } else {
                toast.error(result.message || t('error-update'))
            }
        } catch (error) {
            toast.error(t('error-update'))
        }
    }

    return (
        <form
            onSubmit={handleSubmit}
            className="flex flex-col items-center gap-4 space-y-3"
        >
            <div>
                <div className="flex flex-col">
                    <div className="flex flex-col mb-4">
                        <div className="flex flex-col">
                            <div className="flex font-bold mb-2">
                                {t('ancien')}
                            </div>
                            <div className="relative animate-fade-in-up mb-4">
                                <Input
                                    className="peer block w-full rounded-md border bg-zinc-50 px-2 py-[9px] text-sm outline-none placeholder:text-zinc-500 dark:border-zinc-800 dark:bg-zinc-950"
                                    id="oldPassword"
                                    type={showOldPassword ? 'text' : 'password'}
                                    name="oldPassword"
                                    placeholder={t('ancien-placeholder')}
                                    value={passwords.oldPassword}
                                    onChange={handleInputChange}
                                    required
                                    inputAdornment={
                                        <PasswordVisibility
                                            passwordVisibility={showOldPassword}
                                            showPassword={() => {
                                                handleShowPassword(
                                                    setShowOldPassword
                                                )(showOldPassword)
                                            }}
                                            hidePassword={() => {
                                                handleShowPassword(
                                                    setShowOldPassword
                                                )(showOldPassword)
                                            }}
                                        />
                                    }
                                />
                            </div>
                        </div>
                        <div className="flex flex-col">
                            <div className="flex font-bold mb-2">
                                {t('new')}
                            </div>
                            <div className="relative animate-fade-in-up mb-4">
                                <Input
                                    className="peer block w-full rounded-md border bg-zinc-50 px-2 py-[9px] text-sm outline-none placeholder:text-zinc-500 dark:border-zinc-800 dark:bg-zinc-950"
                                    id="newPassword"
                                    type={showNewPassword ? 'text' : 'password'}
                                    name="newPassword"
                                    placeholder={t('new-placeholder')}
                                    value={passwords.newPassword}
                                    onChange={handleInputChange}
                                    required
                                    inputAdornment={
                                        <PasswordVisibility
                                            passwordVisibility={showNewPassword}
                                            showPassword={() => {
                                                handleShowPassword(
                                                    setShowNewPassword
                                                )(showNewPassword)
                                            }}
                                            hidePassword={() => {
                                                handleShowPassword(
                                                    setShowNewPassword
                                                )(showNewPassword)
                                            }}
                                        />
                                    }
                                />
                            </div>
                        </div>
                        <div className="flex flex-col">
                            <div className="flex font-bold mb-2">
                                {t('confirm')}
                            </div>
                            <div className="relative animate-fade-in-up mb-4">
                                <Input
                                    className="peer block w-full rounded-md border bg-zinc-50 px-2 py-[9px] text-sm outline-none placeholder:text-zinc-500 dark:border-zinc-800 dark:bg-zinc-950"
                                    id="confirmNewPassword"
                                    type={
                                        showNewPasswordConfirmation
                                            ? 'text'
                                            : 'password'
                                    }
                                    name="confirmNewPassword"
                                    placeholder={t('confirm-placeholder')}
                                    value={passwords.confirmNewPassword}
                                    onChange={handleInputChange}
                                    required
                                    inputAdornment={
                                        <PasswordVisibility
                                            passwordVisibility={
                                                showNewPasswordConfirmation
                                            }
                                            showPassword={() => {
                                                handleShowPassword(
                                                    setShowNewPasswordConfirmation
                                                )(showNewPasswordConfirmation)
                                            }}
                                            hidePassword={() => {
                                                handleShowPassword(
                                                    setShowNewPasswordConfirmation
                                                )(showNewPasswordConfirmation)
                                            }}
                                        />
                                    }
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <ChangePwButton isLoading={isChangingPassword} />
            </div>
        </form>
    )
}

function ChangePwButton({ isLoading }: { isLoading: boolean }) {
    const {t} = useTranslation(['app/chat'], { keyPrefix: 'profile.change-password' })
    return (
        <button
            type="submit"
            className={`flex flex-row justify-center items-center my-4 h-10 w-full bg-transparent border border-dinoBotBlue rounded-2xl text-dinoBotBlue hover:bg-dinoBotBlue hover:text-white transition-all duration-300 ${!isLoading ? '' : 'opacity-30'} animate-fade-in-down`}
            aria-disabled={isLoading}
            disabled={isLoading}
        >
            {isLoading ? <IconSpinner /> : t('update')}
        </button>
    )
}