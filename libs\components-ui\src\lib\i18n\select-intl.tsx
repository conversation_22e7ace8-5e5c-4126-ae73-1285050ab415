//TODO - navigation
import React, { useState } from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover'
import { Button } from '../ui/button'
import { Check, ChevronsUpDown } from 'lucide-react'
import { Command, CommandGroup, CommandItem, CommandList } from '../ui/command'
import { cn } from '@dinobot/utils';
import { IconDz, IconFr, IconUk } from '../ui/icons'
import { useTranslation } from 'react-i18next'

export const SelectIntl = () => {
    const [open, setOpen] = useState(false)
    const {t,i18n} = useTranslation(['global'])
    const options = [
        {
            value: 'fr',
            label: (
                <>
                    <IconFr />
                    &nbsp;{t('fr')}
                </>
            )
        },
        {
            value: 'en',
            label: (
                <>
                    <IconUk />
                    &nbsp;{t('en')}
                </>
            )
        },
        {
            value: 'ar',
            label: (
                <>
                    <IconDz />
                    &nbsp;{t('ar')}
                </>
            )
        }
    ]
    const locale = i18n.language
    const handleChange = (selectedOption: string) => {
        // TODO save the selected locale in cookies or local storage
        // navigate(path, {
        //     locale: selectedOption as 'fr' | 'en' | 'ar' | undefined
        // })
    }
    const otherLocale = options?.find(cur => cur.value === locale)
    return (
        <div className="z-30">
            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={open}
                        className="w-36 justify-between"
                    >
                        {otherLocale?.label}
                        <ChevronsUpDown className="ml-2 size-4 shrink-0 opacity-50" />
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="w-36 p-0">
                    <Command>
                        <CommandList>
                            <CommandGroup>
                                {options.map(option => (
                                    <CommandItem
                                        key={option.value}
                                        value={option.value}
                                        onSelect={currentValue => {
                                            handleChange(currentValue)
                                            setOpen(false)
                                        }}
                                    >
                                        <Check
                                            className={cn(
                                                'mr-2 size-4',
                                                otherLocale?.value ===
                                                    option.value
                                                    ? 'opacity-100'
                                                    : 'opacity-0'
                                            )}
                                        />
                                        {option.label}
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>
        </div>
    )
}
