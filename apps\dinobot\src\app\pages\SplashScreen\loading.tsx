import React, { useState, useEffect } from 'react'

export default function Loading() {
    const [width, setWidth] = useState(0)

    useEffect(() => {
        const timeout = setTimeout(() => {
            setWidth(90)
        }, 50)

        return () => clearTimeout(timeout)
    }, [])

    return (
        <div
            style={{ width: `${width}%` }}
            className="fixed top-0 left-0 h-2 bg-blue-500 z-[100000] animate-pulse transition-all duration-1000"
        ></div>
    )
}
