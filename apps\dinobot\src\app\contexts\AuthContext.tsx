import { createContext, useContext, ReactNode } from 'react';
import { useExamsStore } from '@dinobot/stores';
import { useAuthApiClient } from './AppContext';
import Cookies from 'js-cookie';

interface AuthContextType {
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { setExercise } = useExamsStore();
  const authApiClient = useAuthApiClient();

  const logout = async () => {
    try {
      const response = await authApiClient.post('/api/auth/logout');

      if (response.status === 200) {
        setExercise(null);
        Cookies.remove('session');
        Cookies.set('feature', 'Chat');
        window.location.href = '/login';
      } else {
        console.error('Logout failed:', response.statusText);
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const value = {
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}