import { useTranslation } from 'react-i18next'
import EvaluationForm from './components/evaluation-form'
import React from 'react'
import { getLangDir } from 'rtl-detect'

const ScheduleAssessment = () => {
    const { t, i18n } = useTranslation(['teacher.myClass.evaluation.schedule'])
    const dir = getLangDir(i18n.language)

    return (
        <div className="size-full" dir={dir}>
            <div className="size-full flex flex-col gap-1">
                <EvaluationForm />
                <p className="text-center text-dinoBotGray">
                    {t('footer_title')}
                </p>
            </div>
        </div>
    )
}

export default ScheduleAssessment
