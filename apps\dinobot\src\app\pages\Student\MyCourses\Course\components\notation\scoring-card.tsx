import moment from 'moment'
import React from 'react'
import { selectorNotationStore } from '../../../stores/notation.store'
import { PlannedEvaluationPartialWithRelations } from '@dinobot/prisma'
import { cn } from '@dinobot/utils'

type ScoringCardProps = {
    plannedEvaluation: PlannedEvaluationPartialWithRelations
}

const ScoringCard = ({ plannedEvaluation }: ScoringCardProps) => {
    const setSelectedPlannedEvaluations =
        selectorNotationStore.use.setSelectedPlannedEvaluations()
    const selectedPlannedEvaluations =
        selectorNotationStore.use.selectedPlannedEvaluations()
    return (
        <div
            className={cn(
                'flex items-center text-dinoBotDarkGray py-2 px-4 h-20 bg-dinoBotLightGray/40 rounded-xl justify-between',
                selectedPlannedEvaluations?.id === plannedEvaluation.id &&
                    'bg-dinoBotLightSky text-dinoBotBlue'
            )}
            onClick={() => setSelectedPlannedEvaluations(plannedEvaluation)}
        >
            <div className="">
                <h3 className="font-medium text-lg ">
                    {plannedEvaluation.title}
                </h3>
                <p>
                    {moment(plannedEvaluation.submitCorrectedAt).format(
                        'DD/MM/YYYY'
                    )}
                </p>
            </div>
            <p>{plannedEvaluation.studentSubmission?.at(0)?.globalScore}</p>
        </div>
    )
}

export default ScoringCard
