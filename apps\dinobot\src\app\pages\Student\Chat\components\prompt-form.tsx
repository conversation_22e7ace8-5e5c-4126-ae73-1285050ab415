'use client'

import React, { useRef ,useEffect, useState } from 'react'
import Textarea from 'react-textarea-autosize'
import axios from 'axios'

import { Button,IconArrowElbow,
    type messageFileType,
    Tooltip,
    TooltipContent,
    TooltipTrigger
} from '@dinobot/components-ui'
import { useEnterSubmit } from '@dinobot/hooks'
import { Paperclip, Radical, X } from 'lucide-react'
import { Circle } from 'rc-progress'
import Cookies from 'js-cookie';
// import {  } from '@/lib/stores/record-voice-store/use-record-voice-store'
import { useCortexStore,useExamsStore,/*,useRecordVoiceStore*/ 
useRecordVoiceStore} from '@dinobot/stores'
// TODO - Link axios check
// import { checkFilesAccess } from '@/app/[locale]/actions'
import { useTranslation } from 'react-i18next';
import { getLangDir } from 'rtl-detect'
import { toast } from 'sonner'
import { ChatTextArea } from '@dinobot/components-ui'
import { UIMessage, ChatRequestOptions } from 'ai'
import { fileToBase64 } from '@dinobot/utils'

export const PromptForm = React.memo(function PromptForm({
    input,
    setInput,
    imageFile,
    setImageFile,
    fileExtension,
    setFileExtension,
    HandleSubmit,
    status,
    addFileData,
    disabled
}: {
    input: string
    setInput: (
        e:
            | React.ChangeEvent<HTMLInputElement>
            | React.ChangeEvent<HTMLTextAreaElement>
    ) => void
    imageFile: string
    setImageFile: (value: string) => void
    fileExtension: string
    setFileExtension: (value: string) => void
    messages: UIMessage[]
    status: 'submitted' | 'streaming' | 'ready' | 'error'
    addFileData: (data: messageFileType) => void
    HandleSubmit: (
        event?: {
            preventDefault?: () => void
        },
        chatRequestOptions?: ChatRequestOptions
    ) => void
    disabled: boolean
}) {
    const { formRef } = useEnterSubmit()
    const inputRef = useRef<HTMLTextAreaElement>(null)
    const [selectedFile, setSelectedFile] = useState<any>(null)
    const [imageHovered, setImageHovered] = useState(false)
    const fileInputRef = useRef<HTMLInputElement>(null)
    const [errorMessage, setErrorMessage] = useState<string | null>(null)
    const [isUploadDisabled, setIsUploadDisabled] = useState(false)
    const { openCortexDialog, reset } = useCortexStore()
    const [visualizerOpen] = useState(false)

    const {t,i18n} = useTranslation(['app/chat'],{keyPrefix:'emptyScreen'})
    const locale = i18n.language
    const direction = getLangDir(locale)
    /*   const [flag, setFlag] = useState(true);

  React.useEffect(() => {
    const interval = setInterval(() => {
      setFlag(prevFlag => !prevFlag);
    }, 1000);

    return () => clearInterval(interval); 
  }, []); */
    // const refAudio = React.useRef<refAudioProps>(null)
    const [percentage, setPercentage] = React.useState<number>(0)
    const [isUploading, setIsUploading] = React.useState<boolean>(false)

    // const { isStreamingText, setIsStreaming } = useStreamingStore()

    const feature = Cookies.get('feature')

    const { exercise } = useExamsStore()

    const { clearRecordedAudio, transcriptionResult } = useRecordVoiceStore()
    //const [ recording, setRecording ] = useState<boolean>(false);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        if (file) {
            setSelectedFile({
                name: file.name,
                file: URL.createObjectURL(file)
            })
            handleUpload(file)
        }
    }

    const handleUpload = async (file: File) => {
        // TODO - Link axios check

        // const result = await checkFilesAccess()

        // if (result?.type === 'error') {
        //     setErrorMessage(result.message || '')
        //     setIsUploadDisabled(true)
        //     setSelectedFile(null)
        //     return
        // }

        setErrorMessage(null)
        setIsUploadDisabled(false)

        setIsUploading(true)
        const formData = new FormData()
        formData.append('image', file)

        try {
            const response = await axios.post('/api/uploadImage', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                },
                onUploadProgress: (progressEvent: any) => {
                    const progress = Math.round(
                        (progressEvent.loaded / progressEvent.total) * 100
                    )
                    setPercentage(progress)
                }
            })

            if (response.status !== 200) {
                throw new Error('Failed to upload image')
            }

            const { originalName, extension } = response.data
            setImageFile(originalName)
            setFileExtension(extension)
            addFileData({
                id: Date.now().toString(),
                name: `${originalName}-${Date.now()}.${extension || 'png'}`,
                file: `data:${file.type};base64,${await fileToBase64(file)}`,
                userId: '',
                messageId: '',
                createdAt: new Date()
            })
        } catch (error) {
            setSelectedFile(null)
        } finally {
            setIsUploading(false)
        }
    }

    const handleButtonClick = () => {
        if (fileInputRef.current && !isUploadDisabled) {
            fileInputRef.current.click()
        }
    }

    React.useEffect(() => {
        if (inputRef.current) {
            inputRef.current.focus()
        }
    }, [])

    const handleSubmit = async () => {
        // setIsStreaming(true)
        HandleSubmit()
        setSelectedFile(null)

        const value = input.trim()
        setInput({
            target: { value: '' }
        } as React.ChangeEvent<HTMLInputElement>)
        reset()

        // if (audio && !visualizerOpen) {
        //     return
        // }

        if (!value && !imageFile && !transcriptionResult) return

        // Optimistically add user message UI
        // console.log('Optimistically adding user message UI')
        // setMessages((currentMessages: any) => [
        //     ...currentMessages,
        //     {
        //         id: nanoid(),
        //         display: (
        //             <UserMessage
        //                 audio={transcriptionResult ? base64RecordedAudio : ''}
        //                 transcribedAudio={transcriptionResult}
        //                 file={selectedFile}
        //                 audioDuration={audioDuration}
        //             >
        //                 {value}
        //             </UserMessage>
        //         )
        //     }
        // ])

        clearRecordedAudio()

        // Submit and get response message
        //console.log("Transcription results : " + transcriptionResult)
        try {
            clearRecordedAudio()
            setImageFile('')
            setSelectedFile(null)
        } catch {
            toast.error('veuillez réessayer')
            // refAudio.current?.toggleMicrophone(true)
        }
    }
    const examDisableCondition =
        feature === 'Exam' ? (exercise ? false : true) : false

    // Handle submit
    useEffect(() => {
        if (transcriptionResult && visualizerOpen) {
            handleSubmit()
        }
    }, [transcriptionResult])

    // const handleCloseVisualizer = async () => {
    //     try {
    //         stopRecording(false)
    //         // refAudio.current?.stopCurrentAudio()
    //         // await refAudio.current?.toggleMicrophone()
    //         cleanup()
    //         setVisualizerOpen(false)
    //         setIsVisualizerOpen(false)
    //         setIsStreaming(false)
    //     } catch (error) {
    //         console.error('Error closing visualizer:', error)
    //         // Force cleanup even if there's an error
    //         cleanup()
    //         setVisualizerOpen(false)
    //         setIsVisualizerOpen(false)
    //         setIsStreaming(false)
    //     }
    // }

    // const handleOpenVisualizer = async () => {
    //     try {
    //         setVisualizerOpen(true)
    //         setIsVisualizerOpen(true)
    //         // Wait a small delay to ensure DOM is updated
    //         await new Promise(resolve => setTimeout(resolve, 100))
    //         await refAudio.current?.toggleMicrophone()
    //         startRecording()
    //     } catch (error) {
    //         console.error('Error opening visualizer:', error)
    //         // Cleanup if there's an error
    //         handleCloseVisualizer()
    //     }
    // }

    return (
        <form
            className="flex flex-col items-center"
            ref={formRef}
            onSubmit={async e => {
                e.preventDefault()
                //console.log("We submit something...")
                // Blur focus on mobile
                if (window.innerWidth < 600) {
                    ;(e.target as HTMLFormElement).message?.blur()
                }

                handleSubmit()
            }}
        >
            {errorMessage && (
                <div className="w-full text-red-500 text-sm mb-2">
                    {errorMessage}
                </div>
            )}

            <div className="relative flex max-h-40  w-full grow flex-col bg-background sm:rounded-md sm:border mt-2">
                <input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={handleFileChange}
                    ref={fileInputRef}
                    style={{ display: 'none' }}
                    onClick={e => e.stopPropagation()}
                />

                {/* <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            type="button"
                            onClick={async () => {
                                if (!visualizerOpen) {
                                    handleOpenVisualizer()
                                } else {
                                    handleCloseVisualizer()
                                }
                            }}
                            disabled={disabled || examDisableCondition}
                            className={`${disabled ? 'cursor-not-allowed' : ''} z-10 absolute ${direction === 'ltr' ? 'right-[150px] sm:right-[136px]' : 'left-[150px] sm:left-[136px]'} top-1/2 -translate-y-1/2 size-8 bg-background p-0 rounded-full ${recording ? 'text-dinoBotRed hover:bg-dinoBotRed hover:text-white' : 'text-dinoBotBlue hover:bg-dinoBotBlue hover:text-white'} transition-all duration-500`}
                        >
                            <Mic />
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent
                        className={`${recording ? 'bg-dinoBotRed' : 'bg-dinoBotBlue'}`}
                    >
                        {t('speek')}
                    </TooltipContent> 
                </Tooltip>*/}

                {/* {isVisualizerOpen && (
                    <VoiceVisualizer
                        ref={refAudio}
                        startRecording={startRecording}
                        stopRecording={stopRecording}
                        handleSubmit={handleSubmit}
                        onClose={handleCloseVisualizer}
                        className={`h-[200px] xl:h-[465px] 2xl:h-[783px] w-[calc(100%+4rem)] text-white bg-white border border-dinoBotBlue rounded-xl p-1 text-sm font-bold absolute ${direction === 'ltr' ? '-right-4 ' : '-left-4 '} top-[-202px] xl:top-[-470px] 2xl:top-[-790px]`}
                        isRecording={recording}
                    />
                )} */}

                {selectedFile ? (
                    <div className="relative bottom-0">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div
                                    onClick={() => {
                                        setSelectedFile(null)
                                        setImageFile('')
                                        setSelectedFile(null)
                                    }}
                                    onMouseEnter={() =>
                                        setImageHovered(true)
                                    }
                                    onMouseLeave={() =>
                                        setImageHovered(false)
                                    }
                                    className={`flex items-center justify-center absolute ${direction === 'ltr' ? 'right-10 sm:right-16' : 'left-10 sm:left-16'} bottom-0 size-20 p-0  cursor-pointer border rounded-md z-20`}
                                >
                                    <div className="absolute top-[-36%] w-28 text-xs flex flex-row bg-white border rounded-md p-1">
                                        <div className="truncate">
                                            {imageFile.replace(
                                                /\.[^/.]+$/,
                                                ''
                                            )}
                                        </div>
                                        .{fileExtension}
                                    </div>
                                    <div
                                        className={`absolute ${direction === 'ltr' ? 'right-0 ' : 'left-0'} bottom-0 bg-white ${
                                            imageHovered
                                                ? 'opacity-50'
                                                : 'opacity-30'
                                        } size-full transition-all duration-500`}
                                    ></div>
                                    {isUploading ? (
                                        <Circle
                                            className={`absolute ${direction === 'ltr' ? 'right-0 ' : 'left-0'} bottom-0`}
                                            percent={percentage}
                                            strokeWidth={8}
                                            strokeColor="#00bd95"
                                        />
                                    ) : null}
                                    <div
                                        className={`flex items-center justify-center size-10 ${
                                            imageHovered
                                                ? 'opacity-100'
                                                : 'opacity-80'
                                        } text-dinoBotCyan rounded-full border border-dinoBotCyan z-20 transition-all duration-500`}
                                    >
                                        <X size={30} />
                                    </div>
                                </div>
                            </TooltipTrigger>
                            <TooltipContent className="bg-dinoBotCyan">
                                {selectedFile.name.endsWith('pdf')
                                    ? t('rm-pdf')
                                    : t('rm-img')}
                            </TooltipContent>
                        </Tooltip>

                        {selectedFile.name.endsWith('pdf') ? (
                            <img
                                className={`absolute ${direction === 'ltr' ? 'right-10 sm:right-16' : 'left-10 sm:left-16'} bottom-0 size-20 bg-background p-0  border rounded-md z-10 object-cover`}
                                src={'/pdf-logo.jpg'}
                                width={80}
                                height={80}
                                alt="Votre fichier PDF"
                            />
                        ) : (
                            <img
                                className={`absolute ${direction === 'ltr' ? 'right-10 sm:right-16' : 'left-10 sm:left-16'} bottom-0 size-20 bg-background p-0 border rounded-md z-10 object-cover`}
                                src={selectedFile.file}
                                width={80}
                                height={80}
                                alt="Votre img"
                            />
                        )}
                    </div>
                ) : (
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                type="button"
                                //variant="outline"
                                size="icon"
                                disabled={
                                    disabled ||
                                    examDisableCondition ||
                                    isUploadDisabled
                                }
                                className={`${disabled ? 'cursor-not-allowed' : ''} z-10 absolute ${direction === 'ltr' ? 'right-10 sm:right-14' : 'left-10 sm:left-14'} top-1/2 -translate-y-1/2 size-8 rounded-full bg-background p-0 text-dinoBotCyan hover:text-white dark:hover:text-gray-50 hover:bg-dinoBotCyan hover:border-dinoBotCyan hover:border transition-all duration-500`}
                                onClick={handleButtonClick}
                            >
                                {/* <ImagePlus className="" /> */}
                                <Paperclip />
                                <span className="sr-only">
                                    {t('add-file')}
                                </span>
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent className="bg-dinoBotCyan">
                            {t('add-file')}
                        </TooltipContent>
                    </Tooltip>
                )}

                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            type="button"
                            //variant="outline"
                            size="icon"
                            disabled={
                                disabled ||
                                examDisableCondition ||
                                isUploadDisabled
                            }
                            className={`${disabled ? 'cursor-not-allowed' : ''} z-10  absolute ${direction === 'ltr' ? 'right-24' : 'left-24'} top-1/2 -translate-y-1/2 size-8 rounded-full bg-background p-0 text-dinoBotRed hover:text-white dark:hover:text-gray-50 hover:bg-dinoBotRed hover:border-dinoBotRed hover:border transition-all duration-500`}
                            onClick={openCortexDialog}
                        >
                            {/* <ImagePlus className="" /> */}
                            <Radical />
                            <span className="sr-only">{t('formule')}</span>
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent className="bg-dinoBotRed">
                        {t('formule')}
                    </TooltipContent>
                </Tooltip>

                <Textarea
                    ref={inputRef}
                    tabIndex={0}
                    //onKeyDown={onKeyDown}
                    placeholder={disabled ? t('connect-toi') : t('send')}
                    //className={`${disabled ? `cursor-not-allowed placeholder:text-dinoBotRed placeholder:font-bold ${flag ? "animate-moveAround duration-500" : "animate-bounce duration-500"} ` : ""} min-h-[60px] w-full resize-none bg-transparent pl-4 pr-[150px] py-[1.3rem] focus-within:outline-none sm:text-sm`}
                    className={`hidden ${disabled ? ` cursor-not-allowed placeholder:text-dinoBotRed/50 placeholder:font-bold` : ''} min-h-[60px] w-full resize-none bg-transparent pl-4 pr-[150px] py-[1.3rem] focus-within:outline-none sm:text-sm`}
                    autoFocus
                    spellCheck={false}
                    autoComplete="off"
                    autoCorrect="off"
                    name="message"
                    rows={1}
                    value={input}
                    onChange={e => setInput(e)}
                    disabled={disabled || examDisableCondition}
                />
                <ChatTextArea
                    placeholder={disabled ? t('connect-toi') : t('send')}
                    onChange={value =>
                        setInput({
                            target: { value }
                        } as React.ChangeEvent<HTMLInputElement>)
                    }
                    theme={'snow'}
                    className={`chat border-none ${disabled ? ` cursor-not-allowed placeholder:text-dinoBotRed/50 placeholder:font-bold` : ''}  w-3/4 resize-none bg-transparent  focus-within:outline-none sm:text-sm ${direction === 'rtl' ? 'rtl' : ''}`}
                />

                <div
                    className={`${disabled ? 'cursor-not-allowed' : ''} absolute ${direction === 'ltr' ? 'right-0 sm:right-1' : 'left-0 sm:left-1'} top-1/2 -translate-y-1/2 `}
                >
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                className={` bg-dinoBotBlue rounded-xl hover:bg-white hover:text-dinoBotBlue hover:border-dinoBotBlue hover:border`}
                                type="submit"
                                size="icon"
                                disabled={
                                    input.trim() === '' ||
                                    status === 'streaming' ||
                                    disabled ||
                                    examDisableCondition
                                }
                            >
                                <IconArrowElbow
                                    className={
                                        direction === 'ltr'
                                            ? ''
                                            : '-scale-x-100'
                                    }
                                />
                                <span className="sr-only">
                                    {t('send-m')}
                                </span>
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent className="bg-dinoBotBlue">
                            {t('send-m')}
                        </TooltipContent>
                    </Tooltip>
                </div>
            </div>
        </form>
    )
})
