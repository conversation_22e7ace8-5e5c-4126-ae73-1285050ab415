import { create } from 'zustand'
import { createSelectors } from '@dinobot/stores'
import { ControlQuestionMedia } from '@dinobot/prisma'

interface CtrlMediaState {
    attachments: ControlQuestionMedia[] | null
    index: number
}

interface CtrlMediaStateActions {
    setAttachments: (
        attachments: ControlQuestionMedia[] | null,
        index: number | undefined
    ) => void
    next: () => void
    previous: () => void
}

const initialMediaState: CtrlMediaState = {
    attachments: null,
    index: 0
}

const useCtrlMediaStore = create<CtrlMediaState & CtrlMediaStateActions>()(
    (set, get) => ({
        ...initialMediaState,
        setAttachments(attachments, index = 0) {
            set(state => ({ ...state, attachments, index }))
        },
        next() {
            const attachments = get().attachments
            if (attachments && attachments.length > 0) {
                const length = attachments.length
                set(state => ({
                    ...state,
                    index: (state.index + 1) % length
                }))
            }
        },
        previous() {
            const attachments = get().attachments
            if (attachments && attachments.length > 0) {
                const length = attachments.length
                set(state => ({
                    ...state,
                    index: (state.index - 1 + length) % length
                }))
            }
        }
    })
)

export default useCtrlMediaStore
export const selectUseCtrlMediaStore = createSelectors(useCtrlMediaStore)
