import { createSelectors } from '@dinobot/stores';
import { Home, Timer } from 'lucide-react';
import { createElement, ReactElement } from 'react';
import { toast } from 'sonner';
import { create } from 'zustand';

// Types for the store - simplified for now
type Class = {
  id: string;
  name: string;
};

type EventRow = {
  id: string;
  title: string;
  date: Date;
  type: filterType;
};

type EventList = EventRow[];

type controleStoreType = controleStateType & controleActionType;
export type eventCalendarType = {
  id: string;
  title: string;
  start: Date;
  end: Date;
  color: string;
  type: filterType;
  open: boolean;
  icone: ReactElement;
};
export type filterType = 'TOUS' | 'HOMEWORK' | 'CONTROL' | 'SORTIE';
type controleStateType = {
  events: EventList;
  classes: Class[];
  eventsCalendar: eventCalendarType[];
  open: boolean;
  filter: filterType;
};

type controleActionType = {
  setEvents: (events: EventList) => void;
  setClasses: (classes: Class[]) => void;
  addEvent: (event: EventRow, classids: string[], apiClient: any) => void;
  updateEvent: (event: EventRow, classids: string[], apiClient: any) => void;
  deleteEvent: (event: EventRow, apiClient: any) => void;
  setOpen: (open: boolean, isupdate: boolean, id?: string) => void;
  setFilter: (filter: filterType) => void;
};

const inistialCalendarState: controleStateType = {
  events: [],
  classes: [],
  eventsCalendar: [],
  open: false,
  filter: 'TOUS',
};
export const useCalendarStore = create<controleStoreType>((set) => ({
  ...inistialCalendarState,
  setEvents(events) {
    set((state) => ({
      ...state,
      events,
      eventsCalendar: events.map((e) => ({
        id: e.id,
        title: e.title,
        start: e.date,
        end: e.date,
        type: e.type,
        open: false,
        color: e.type === 'CONTROL' ? '#FF5E0E' : '#2AAFFB',
        icone: createElement(e.type === 'HOMEWORK' ? Home : Timer),
      })),
    }));
  },
  setClasses(classes) {
    set((state) => ({ ...state, classes: classes }));
  },
  async addEvent(event, classids, apiClient) {
    try {
      const response = await apiClient.post('/api/events', {
        ...event,
        classIds: classids,
      });
      const newEvent = response.data;
      const eventCalendar: eventCalendarType = {
        id: newEvent.id,
        title: newEvent.title,
        start: new Date(newEvent.date),
        end: new Date(newEvent.date),
        open: false,
        type: newEvent.type,
        color: newEvent.type === 'CONTROL' ? '#FF5E0E' : '#2AAFFB',
        icone: createElement(newEvent.type === 'HOMEWORK' ? Home : Timer),
      };
      set((state) => ({
        ...state,
        events: [...state.events, newEvent],
        eventsCalendar: [...state.eventsCalendar, eventCalendar],
      }));
      toast.success("L'événement a été ajouté avec succès");
    } catch (error) {
      toast.error("Une erreur s'est produite lors de l'ajout de l'événement");
    }
  },
  async updateEvent(event, classids, apiClient) {
    try {
      const response = await apiClient.put(`/api/events/${event.id}`, {
        ...event,
        classIds: classids,
      });
      const updatedEvent = response.data;
      const eventCalendar: eventCalendarType = {
        id: updatedEvent.id,
        title: updatedEvent.title,
        start: new Date(updatedEvent.date),
        end: new Date(updatedEvent.date),
        open: false,
        type: updatedEvent.type,
        color: updatedEvent.type === 'CONTROL' ? '#FF5E0E' : '#2AAFFB',
        icone: createElement(updatedEvent.type === 'HOMEWORK' ? Home : Timer),
      };
      set((state) => ({
        ...state,
        events: state.events.map((e) =>
          e.id === updatedEvent.id ? updatedEvent : e,
        ),
        eventsCalendar: state.eventsCalendar.map((e) =>
          e.id === updatedEvent.id ? eventCalendar : e,
        ),
      }));
      toast.success("L'événement a été mis à jour avec succès");
    } catch (error) {
      toast.error(
        "Une erreur s'est produite lors de la mise à jour de l'événement",
      );
    }
  },
  async deleteEvent(event, apiClient) {
    try {
      await apiClient.delete(`/api/events/${event.id}`);
      set((state) => ({
        ...state,
        events: state.events.filter((e) => e.id !== event.id),
        eventsCalendar: state.eventsCalendar.filter((e) => e.id !== event.id),
      }));
      toast.success("L'événement a été supprimé avec succès");
    } catch (error) {
      toast.error(
        "Une erreur s'est produite lors de la suppression de l'événement",
      );
    }
  },
  setFilter(filter) {
    set((state) => ({ ...state, filter }));
  },
  setOpen(open, isupdate, id) {
    if (isupdate)
      set((state) => ({
        ...state,
        eventsCalendar: state.eventsCalendar.map((e) =>
          e.id === id ? { ...e, open } : e,
        ),
      }));
    else set((state) => ({ ...state, open: open }));
  },
}));
export const selectUseCalendarStore = createSelectors(useCalendarStore)
