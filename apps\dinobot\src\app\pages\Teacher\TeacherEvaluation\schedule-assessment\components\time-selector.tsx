import React from 'react'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@dinobot/components-ui'
import { Clock } from 'lucide-react'

interface TimeSelectorProps {
    value: { hours: string; minutes: string }
    onChange: (time: { hours: string; minutes: string }) => void
}

export function TimeSelector({ value, onChange }: TimeSelectorProps) {
    const hours = Array.from({ length: 24 }, (_, i) =>
        i.toString().padStart(2, '0')
    )
    const minutes = Array.from({ length: 60 }, (_, i) =>
        i.toString().padStart(2, '0')
    )

    return (
        <div className="flex flex-col space-y-4">
            <div className="flex items-center space-x-2">
                <div className="relative">
                    <Clock className="absolute left-2 top-2.5 size-4 text-muted-foreground" />
                    <Select
                        value={value.hours}
                        onValueChange={newHour =>
                            onChange({ ...value, hours: newHour })
                        }
                    >
                        <SelectTrigger className="w-[80px] pl-8">
                            <SelectValue placeholder="Hour" />
                        </SelectTrigger>
                        <SelectContent className="h-[200px] w-[80px]">
                            {hours.map(hour => (
                                <SelectItem key={hour} value={hour}>
                                    {hour}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
                <span className="text-2xl font-semibold text-muted-foreground">
                    :
                </span>
                <Select
                    value={value.minutes}
                    onValueChange={newMinute =>
                        onChange({ ...value, minutes: newMinute })
                    }
                >
                    <SelectTrigger className="w-[60px]">
                        <SelectValue placeholder="Minute" />
                    </SelectTrigger>
                    <SelectContent className="h-[200px] w-[80px]">
                        {minutes.map(minute => (
                            <SelectItem key={minute} value={minute}>
                                {minute}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            </div>
        </div>
    )
}
