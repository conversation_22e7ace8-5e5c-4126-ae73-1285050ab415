import * as React from 'react'
import { useRouteLoaderData } from 'react-router-dom'
import { FeatureFlag, FeatureFlagNameSchema } from '@dinobot/prisma'
import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../contexts/AppContext'

export function useFeatureFlags() {
    const featureFlags = useRouteLoaderData('feature-flags') as FeatureFlag[] | null

    const flagsMap = React.useMemo(() => {
        if (!Array.isArray(featureFlags)) {
            return new Map<string, boolean>()
        }
        return new Map(
            featureFlags.map(flag => [flag.featureName, flag.isEnabled])
        )
    }, [featureFlags])

    const getFlag = React.useCallback((flagName: keyof typeof FeatureFlagNameSchema.Enum): boolean => {
        return flagsMap.get(FeatureFlagNameSchema.Enum[flagName]) || false
    }, [flagsMap])

    const flags = React.useMemo(() => ({
        exerciseModeEnabled: getFlag('STUDENT_EXERCISE_MODE'),
        evaluationModeEnabled: getFlag('STUDENT_EVALUATION_MODE'),
        examModeEnabled: getFlag('STUDENT_EXAM_MODE'),
        chatModeEnabled: getFlag('STUDENT_CHAT_MODE'),
        classesViewEnabled: getFlag('STUDENT_CLASSES_VIEW'),
    }), [getFlag])

    return {
        ...flags,
        getFlag,
        flagsMap,
        rawFeatureFlags: featureFlags
    }
}

// Hook pour les feature routes (comme dans MyCourses)
export function useFeatureRoutes(featureName: keyof typeof FeatureFlagNameSchema.Enum) {
    const apiClient = useAuthApiClient()
    
    return useQuery({
        queryKey: ['featureRoutes', featureName],
        queryFn: async (): Promise<string[]> => {
            const response = await apiClient.get(`/api/features/routes/${FeatureFlagNameSchema.Enum[featureName]}`)
            return response.data.routes || []
        }
    })
}