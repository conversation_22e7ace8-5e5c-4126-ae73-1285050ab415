//Hooks
import * as React from 'react'

import { LogIn } from 'lucide-react'

import Cookies from 'js-cookie'


import { useTranslation } from 'react-i18next'
import { Session } from '@dinobot/utils'
import { User } from '@dinobot/prisma/lib/generated/zod/modelSchema/UserSchema';
import { useFeatureFlags } from '../../hooks/useFeatureFlags'
import SplitScreen from './split-screen'
import { Button, ChatHistory, FilesSidebarMobile, FilesSidebarToggle, SelectIntl, SidebarMobile, SidebarToggle } from '@dinobot/components-ui'
import { SelectTopic } from './select/select-topic'
import { SelectFeature } from './select/select-feature'
import SwitchAvatar from '@dinobot/components-ui/lib/avatar/components/switch-avatar'
import { FileHistory } from '@dinobot/components-ui/lib/files/file-history'
import { Link, useNavigate } from 'react-router-dom'
import DisconnectButton from '../../pages/Auth/login/components/disconnect'
import { useExamsStore, useCtrlModeStore } from '@dinobot/stores'
import { FileText, MessageCircleMore, Star, Timer } from 'lucide-react'



function UserOrLogin({
    isChatModeEnabled
}: {
    isChatModeEnabled: boolean
}) {
    const session = JSON.parse(Cookies.get('session') || '{}') as Session
    const {t,i18n} = useTranslation(['app/headers'],{keyPrefix:"login"})
    const locale = i18n.language
    return (
        <div
            className={`flex flex-row justify-center items-center ${locale === 'ar' ? 'gap-4' : ''}`}
        >
            {session?.user ? (
                <div>
                    {isChatModeEnabled ? (
                        <>
                            <SidebarMobile>
                                <ChatHistory userId={session.user.id} />
                            </SidebarMobile>
                            <SidebarToggle />
                        </>
                    ) : null}
                </div>
            ) : (
                <Link to="/" rel="nofollow">
                    <img
                        src='/dinobot-logo-small.svg'
                        alt="DinoBot Logo"
                        className="min-w-[40px] w-[40px] sm:mr-2"
                    />
                </Link>
            )}
            <div className="flex items-center ">
                {/* <IconSeparator className="size-6 text-muted-foreground/50" /> */}
                {session?.user ? (
                    null
                ) : (
                    <Button
                        asChild
                        className="px-2 mb-1 bg-transparent border border-gray-500 rounded-2xl text-gray-500 hover:bg-gray-500 hover:text-white sm:ml-2 transition-all duration-300 "
                    >
                        <Link to="/login">
                            <LogIn className="block sm:hidden size-4" />
                            <span className="hidden sm:block">
                                {t('title')}
                            </span>
                        </Link>
                    </Button>
                )}
            </div>
        </div>
    )
}

function FilesSideBar() {
    const session = JSON.parse(Cookies.get('session') || '{}') as Session

    return (
        <div>
            {session?.user ? (
                <>
                    <FilesSidebarMobile>
                        <FileHistory userId={session.user.id} />
                    </FilesSidebarMobile>
                    <FilesSidebarToggle />
                </>
            ) : null}
        </div>
    )
}

export function Header({ loginType = true }: { loginType?: boolean }) {
    const session = JSON.parse(Cookies.get('session') || '{}') as Session
    const user = session?.user as User
    const avatarModeEnabled = import.meta.env.APP_AVATAR_MODE === 'true' || false
    const { chatModeEnabled, examModeEnabled, exerciseModeEnabled, evaluationModeEnabled } = useFeatureFlags()
    const navigate = useNavigate()
    const { setopenExamsPopup, setExercise } = useExamsStore()
    const reset = useCtrlModeStore(state => state.reset)
    const { t } = useTranslation(['app/sub-headers'])
    
    const feature = Cookies.get('feature')

    const handleFeatureChange = (value: string) => {
        if (value === 'Exam' || value === 'ExamE') {
            Cookies.set('feature', value)
            Cookies.set('mode', 'GPT 4-o')
            navigate('/exam')
            setopenExamsPopup(true)
            return
        } else if (value === 'Exo') {
            Cookies.set('feature', value)
            Cookies.set('mode', 'GPT 4-o')
            navigate('/exercise')
            reset()
            return
        } else if (value === 'Ctrl') {
            Cookies.set('feature', value)
            Cookies.set('mode', 'GPT 4-o')
            navigate('/controle')
            reset()
            return
        }

        setExercise(null)
        Cookies.set('feature', value)
        navigate('/')
    }

    const getCurrentFeatureLabel = () => {
        switch (feature) {
            case 'Chat':
                return (
                    <div className="flex items-center gap-2">
                        <MessageCircleMore className="w-5 h-5" />
                        <span>{t('chat.title')}</span>
                    </div>
                )
            case 'Exam':
            case 'ExamE':
                return (
                    <div className="flex items-center gap-2">
                        <Star className="w-5 h-5" />
                        <span>{t('exam.title')}</span>
                    </div>
                )
            case 'Ctrl':
                return (
                    <div className="flex items-center gap-2">
                        <Timer className="w-5 h-5" />
                        <span>{t('controle.title')}</span>
                    </div>
                )
            case 'Exo':
                return (
                    <div className="flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        <span>{t('exo.title')}</span>
                    </div>
                )
            default:
                return (
                    <div className="flex items-center gap-2">
                        <FileText className="w-5 h-5" />
                        <span>mode exercise</span>
                    </div>
                )
        }
    }

    return (
        <header className="sticky top-0 z-10 flex items-center h-16 md:h-16 px-4 border-b shrink-0 bg-gradient-to-b from-background/10 via-background/50 to-background/80 backdrop-blur-xl md:grid md:grid-cols-8 shadow-lg shadow-dinoBotCyan/15 flex-row justify-between">
            <div className="sm:w-fit flex items-center col-span-2">
                {loginType && (
                    <React.Suspense
                        fallback={<div className="flex-1 overflow-overlay" />}
                    >
                        <UserOrLogin
                            isChatModeEnabled={chatModeEnabled || examModeEnabled}
                        />
                    </React.Suspense>
                )}
                <SelectFeature />
            </div>
            <div
                className={`w-fit gap-2 md:h-auto flex flex-col md:flex-row justify-start md:justify-center  items-center md:items-start col-span-3 col-start-3 ${session?.user ? '' : 'ml-2'}`}
            >
                {loginType && <SelectTopic user={user} />}
            </div>

            <div className="flex flex-row justify-between items-center col-span-3 col-start-6">
                <div className="sm:w-fit lg:h-auto flex flex-col justify-center items-end">
                    {(chatModeEnabled || examModeEnabled) && (
                        <React.Suspense
                            fallback={
                                <div className="flex-1 overflow-overlay" />
                            }
                        >
                            <FilesSideBar />
                        </React.Suspense>
                    )}
                </div>
                {loginType &&
                    (avatarModeEnabled && session?.user ? (
                        <SwitchAvatar />
                    ) : null)}
                <SplitScreen />
                <SelectIntl />
                {session?.user ? (
                    <DisconnectButton />
                ) : null}
            </div>
        </header>
    )
}
