'use client'

import * as React from 'react'

const FILES_LOCAL_STORAGE_KEY = 'files-sidebar'

interface FilesSidebarContext {
    isFilesSidebarOpen: boolean
    toggleFilesSidebar: () => void
    openFilesSidebar: () => void
    closeFilesSidebar: () => void
    isFilesLoading: boolean
}

const FilesSidebarContext = React.createContext<
    FilesSidebarContext | undefined
>(undefined)

export function useFilesSidebar() {
    const filesContext = React.useContext(FilesSidebarContext)
    if (!filesContext) {
        throw new Error(
            'useFilesSidebarContext must be used within a SidebarProvider'
        )
    }
    return filesContext
}

interface FilesSidebarProviderProps {
    children: React.ReactNode
}

export function FilesSidebarProvider({ children }: FilesSidebarProviderProps) {
    const [isFilesSidebarOpen, setFilesSidebarOpen] = React.useState(false)
    const [isFilesLoading, setFilesLoading] = React.useState(true)

    React.useEffect(() => {
        const value = localStorage.getItem(FILES_LOCAL_STORAGE_KEY)
        if (value) {
            setFilesSidebarOpen(JSON.parse(value))
        }
        setFilesLoading(false)
    }, [])

    const toggleFilesSidebar = () => {
        setFilesSidebarOpen(value => {
            const newState = !value
            localStorage.setItem(
                FILES_LOCAL_STORAGE_KEY,
                JSON.stringify(newState)
            )
            return newState
        })
    }

    const openFilesSidebar = () => {
        setFilesSidebarOpen(false)
    }

    const closeFilesSidebar = () => {
        setFilesSidebarOpen(true)
    }

    if (isFilesLoading) {
        return null
    }

    return (
        <FilesSidebarContext.Provider
            value={{
                isFilesSidebarOpen,
                toggleFilesSidebar,
                openFilesSidebar,
                closeFilesSidebar,
                isFilesLoading
            }}
        >
            {children}
        </FilesSidebarContext.Provider>
    )
}
