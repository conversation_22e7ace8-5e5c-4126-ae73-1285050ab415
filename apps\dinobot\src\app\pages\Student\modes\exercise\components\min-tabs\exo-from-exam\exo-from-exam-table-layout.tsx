import { ColumnDef } from '@tanstack/react-table'
import React from 'react'
import ExoFromExamTable from './exo-from-exam-table'
import { ExoOutput, ExoSortsAndFilters } from '../../../trainig.types'
import { useTranslation } from 'react-i18next'

interface ExoFromExamTableLayoutProps {
    exams: ExoOutput[]
    filtersSortChange: (filters: ExoSortsAndFilters) => void
}

function ExoFromExamTableLayout({
    exams,
    filtersSortChange
}: ExoFromExamTableLayoutProps) {
    const { t } = useTranslation(['app/mode'],{keyPrefix: 'exo.mini-tabs.exam.table'})

    const columns: ColumnDef<ExoOutput>[] = [
        {
            accessorKey: 'id'
        },
        {
            accessorFn: exo => exo.title,
            header: 'Intitulé',
            id: 'exo.title',
            cell: ({ row }) => {
                const title = row.getValue('exo.title')
                return (
                    <div>
                        {title ? (
                            `${title}`
                        ) : (
                            <span className="italic text-dinoBotRed">
                                {t('no-write')}
                            </span>
                        )}
                    </div>
                )
            }
        },
        // {
        //   accessorFn: exo => exo.exam.title,
        //   header: "Titre d'examen",
        //   id:"exam.title",
        //   cell: ({row}) => {
        //     const title = row.getValue("exam.title")
        //     return(
        //       <div>
        //         {title ? `${title}` : <span className="italic text-dinoBotRed">Non renseigné</span>}
        //       </div>
        //     )
        //   },
        // },
        {
            accessorFn: exo => exo.exam.year,
            header: 'Année',
            id: 'exam.year'
        }
    ]

    return (
        <div>
            <ExoFromExamTable
                columns={columns}
                data={exams}
                filtersSortChange={filtersSortChange}
            />
        </div>
    )
}

export default ExoFromExamTableLayout
