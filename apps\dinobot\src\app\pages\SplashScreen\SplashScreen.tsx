import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

const SplashScreen = () => {
  const { t } = useTranslation(['global']);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Smooth fade in animation
    const timer = setTimeout(() => setIsVisible(true), 200);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex flex-col items-center justify-center relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Soft floating shapes */}
        <div className="absolute top-1/4 left-1/4 w-80 h-80 bg-blue-200/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-200/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-3/4 left-3/4 w-64 h-64 bg-indigo-200/20 rounded-full blur-3xl animate-pulse delay-500"></div>

        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(99,102,241,0.05)_1px,transparent_0)] bg-[size:40px_40px]"></div>
      </div>

      {/* Main content */}
      <div
        className={`relative z-10 flex flex-col items-center justify-center transition-all duration-1500 ease-out transform ${isVisible ? 'opacity-100 translate-y-0 scale-100' : 'opacity-0 translate-y-12 scale-95'}`}
      >
        {/* Logo container with soft, friendly styling */}
        <div className="relative mb-12 group">
          {/* Soft outer glow */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-300/30 via-indigo-300/30 to-purple-300/30 rounded-full blur-2xl scale-110 group-hover:scale-115 transition-transform duration-700"></div>

          {/* Main container */}
          <div className="relative bg-white/90 backdrop-blur-xl rounded-full p-8 border border-white/60 shadow-xl shadow-blue-100/50">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-full p-1 border border-blue-100/50">
              <img
                src="/dinobot-logo.svg"
                alt={t('splash.app.name')}
                className="size-28  filter drop-shadow-lg"
              />
            </div>
          </div>

          {/* Gentle rotating ring */}
          <div
            className="absolute inset-0 rounded-full border-2 border-transparent bg-gradient-to-r from-blue-400/40 via-transparent to-indigo-400/40 bg-clip-border animate-spin opacity-40"
            style={{ animationDuration: '12s' }}
          ></div>
        </div>

        {/* App name with warm, educational typography */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent mb-4 tracking-tight">
            {t('splash.app.name')}
          </h1>
          <p className="text-slate-600 text-xl font-medium tracking-wide">
            {t('splash.app.tagline')}
          </p>
          <div className="mt-3 flex items-center justify-center space-x-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
            <span className="text-slate-500 text-sm font-medium">
              {t('splash.ai.powered')}
            </span>
            <div className="w-2 h-2 bg-indigo-400 rounded-full"></div>
          </div>
        </div>

        {/* Clean, educational-themed loader */}
        <div className="relative">
          {/* Outer ring with soft colors */}
          <div className="w-16 h-16 border-4 border-blue-100 rounded-full"></div>

          {/* Animated ring with educational colors */}
          <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-blue-500 border-r-indigo-500 rounded-full animate-spin"></div>

          {/* Center content - brain/learning icon concept */}
          <div className="absolute inset-0 w-16 h-16 flex items-center justify-center">
            <div className="flex space-x-1">
              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse"></div>
              <div className="w-1.5 h-1.5 bg-indigo-500 rounded-full animate-pulse delay-200"></div>
              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse delay-400"></div>
            </div>
          </div>

          {/* Soft glow effect */}
          <div className="absolute inset-0 w-16 h-16 bg-gradient-to-r from-blue-400/10 to-indigo-400/10 rounded-full blur-lg animate-pulse"></div>
        </div>
      </div>

      {/* Educational footer */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center">
        <div className="flex items-center space-x-3 text-slate-400">
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
            />
          </svg>
          <span className="text-sm font-medium tracking-wide">
            {t('splash.footer.message')}
          </span>
          <svg
            className="w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default SplashScreen;
