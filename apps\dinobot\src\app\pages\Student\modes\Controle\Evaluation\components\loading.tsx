import { cn } from '@dinobot/utils'
import React from 'react'

type LoadingProps = {
    className?: string
}
export default function Loading({ className }: LoadingProps) {
    return (
        <div
            className={cn(
                `absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex  z-10 animate-pulse transition-all duration-1000`,
                className
            )}
        >
            <div className="size-12 border-2 border-y-blue-500 border-l-blue-500 animate-spin rounded-full"></div>
        </div>
    )
}
