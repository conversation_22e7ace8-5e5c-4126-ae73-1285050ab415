import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { ThemePartial } from '@dinobot/prisma'
import type { CreateThemeInput } from '../teacherEvaluation.types'

export const useCreateTheme = () => {
    const apiClient = useAuthApiClient()
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: async (data: CreateThemeInput): Promise<ThemePartial> => {
            if (!apiClient) throw new Error('API client not available')
            
            const response = await apiClient.post('/api/themes/', data)
            return response.data
        },
        onSuccess: (newTheme, variables) => {
            // Invalidate and refetch themes for this class
            queryClient.invalidateQueries({ 
                queryKey: ['themes-by-class-id', variables.classId] 
            })
            
            // Optionally update the cache directly
            queryClient.setQueryData(
                ['themes-by-class-id', variables.classId],
                (old: ThemePartial[] | undefined) => {
                    if (!old) return [newTheme]
                    return [...old, newTheme]
                }
            )
        }
    })
}