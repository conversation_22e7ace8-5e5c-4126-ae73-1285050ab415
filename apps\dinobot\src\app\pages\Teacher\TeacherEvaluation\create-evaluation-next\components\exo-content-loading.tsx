import React from 'react'

import { Skeleton } from '@dinobot/components-ui'
import { cn } from '@dinobot/utils'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'

const ExoContentLoading = () => {
    const { i18n } = useTranslation()
    const dir = getLangDir(i18n.language)

    return (
        <div className="relative">
            <Skeleton className="absolute top-9 right-3 size-9 rounded-md" />
            <Skeleton
                className={cn('h-6 w-36 mb-2', dir === 'rtl' && 'ml-auto')}
            />
            <div
                className={cn(
                    'flex flex-col rounded-xl border border-dinoBotLightGray p-4'
                )}
            >
                <div
                    className={cn(
                        'flex gap-4 mb-4',
                        dir === 'rtl' && 'flex-row-reverse'
                    )}
                >
                    <div className="flex gap-2 items-center">
                        <Skeleton className="size-4 rounded-full" />
                        <Skeleton className="h-4 w-24" />
                    </div>
                    <div className="flex gap-2 items-center">
                        <Skeleton className="size-4 rounded-full" />
                        <Skeleton className="h-4 w-24" />
                    </div>
                </div>
                <Skeleton
                    className={cn(
                        'h-4 w-full max-w-md mb-6',
                        dir === 'rtl' && 'ml-auto'
                    )}
                />
                <div className="space-y-6">
                    <div className="space-y-2">
                        <div className="flex justify-between">
                            <Skeleton className="h-5 w-28" />
                            <Skeleton className="h-5 w-20" />
                        </div>
                        <Skeleton className="h-24 w-full rounded-md" />
                    </div>
                </div>

                <div
                    className={cn(
                        'flex mt-4',
                        dir === 'rtl' ? 'justify-end' : 'justify-start'
                    )}
                >
                    <Skeleton className="h-8 w-24" />
                </div>
            </div>
        </div>
    )
}

export default ExoContentLoading
