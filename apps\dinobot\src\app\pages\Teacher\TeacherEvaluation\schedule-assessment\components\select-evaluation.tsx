import { Button } from '@dinobot/components-ui'
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogFooter,
    Di<PERSON>Header,
    DialogTitle,
    DialogTrigger
} from '@dinobot/components-ui'
import { ScrollArea } from '@dinobot/components-ui'
import { Table, TableBody, TableCell, TableRow } from '@dinobot/components-ui'
import { useControls } from '../../hooks/useControls'
import { selectUseScheduleAssessmentStore } from '../../store/use-schedule-assessment-store'
import {
    ColumnDef,
    flexRender,
    getCoreRowModel,
    useReactTable
} from '@tanstack/react-table'
import React, { ReactNode, useEffect, useState } from 'react'
import { ControlPartialWithRelations } from '@dinobot/prisma'

type SelectEvaluationProps = {
    children: ReactNode
    open: boolean
    setOpen: React.Dispatch<React.SetStateAction<boolean>>
}
const SelectEvaluation = ({
    children,
    open,
    setOpen
}: SelectEvaluationProps) => {
    const [columnVisibility] = useState({ id: false })
    const setControls = selectUseScheduleAssessmentStore.use.setControls()
    const control = selectUseScheduleAssessmentStore.use.controls()
    const setSelectedControl =
        selectUseScheduleAssessmentStore.use.setSelectedControl()
    const selectedControl =
        selectUseScheduleAssessmentStore.use.selectedControl()

    const { data: controls, isLoading, error } = useControls({})

    useEffect(() => {
        if (controls) {
            setControls(controls)
        }
    }, [controls, setControls])
    const columns: ColumnDef<ControlPartialWithRelations>[] = [
        {
            accessorKey: 'id'
        },
        {
            accessorFn: controle => controle.name,
            id: 'name',
            cell: ({ row }) => {
                const value: string = row.getValue('name')
                return <span className="font-medium">{value}</span>
            }
        },
        {
            accessorFn: controle => controle.domainName,
            id: 'domain',
            cell: ({ row }) => {
                const value: number = row.getValue('domain')
                return <span>{value}</span>
            }
        },
        {
            accessorFn: controle => controle.exercises?.length,
            id: 'groups',
            cell: ({ row }) => {
                const value: number = row.getValue('groups')
                return <span>{value} exercice</span>
            }
        },
        {
            accessorFn: controle => controle.levelName,
            id: 'annes',
            cell: ({ row }) => {
                const value: string = row.getValue('annes')
                return <span>{value}</span>
            }
        }
        // {
        //     accessorFn:controle=>control.id,
        //     id:"actions",
        //     cell:({row})=>{
        //         const value:string=row.getValue('id')
        //         return(
        //             <>
        //                 <EditClass data={classes.find(c=>c.id===value)!} btnTitle={<IconEdit className='size-6' />}/>
        //                 <Button variant='link' className='text-dinoBotGray' onClick={()=>setArchiveClass(value)}><Archive /></Button>
        //             </>
        //         )
        //     }
        // }
    ]
    const table = useReactTable({
        data: control,
        columns,
        getCoreRowModel: getCoreRowModel(),
        state: {
            columnVisibility
        }
    })
    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger>{children}</DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Are you absolutely sure?</DialogTitle>
                    <DialogDescription></DialogDescription>
                </DialogHeader>
                <ScrollArea className="max-h-[650px] custom-scroller">
                    <Table className="overflow-y-auto custom-scroller">
                        <TableBody>
                            {isLoading || error ? (
                                <TableRow>
                                    <TableCell
                                        colSpan={columns.length}
                                        className="h-24 text-center"
                                    >
                                        {/* Loading or error state - keeping original empty state */}
                                    </TableCell>
                                </TableRow>
                            ) : table.getRowModel().rows?.length ? (
                                table.getRowModel().rows.map(row => (
                                    <TableRow
                                        key={row.id}
                                        data-state={
                                            row.getIsSelected() && 'selected'
                                        }
                                        className={`${row.original.id === selectedControl?.id ? 'bg-dinoBotVibrantBlue/20 text-dinoBotBlue hover:bg-dinoBotVibrantBlue/30' : ''}`}
                                        onClick={() =>
                                            setSelectedControl(row.original)
                                        }
                                    >
                                        {row.getVisibleCells().map(cell => (
                                            <TableCell key={cell.id}>
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </TableCell>
                                        ))}
                                    </TableRow>
                                ))
                            ) : (
                                <TableRow>
                                    <TableCell
                                        colSpan={columns.length}
                                        className="h-24 text-center"
                                    >
                                        Aucune controle trouvée.
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </ScrollArea>
                <DialogFooter className="w-full sm:justify-end">
                    <Button
                        variant="link"
                        className="text-dinoBotBlue underline"
                        disabled={!selectedControl}
                        type="submit"
                        onClick={() => setOpen(false)}
                    >
                        Enregistrer
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

export default SelectEvaluation
