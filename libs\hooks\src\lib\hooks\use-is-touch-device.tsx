import { useEffect, useState } from 'react'

const useIsTouchDevice = () => {
    const [isTouchDevice, setIsTouchDevice] = useState(false)

    useEffect(() => {
        const checkTouchDevice = () => {
            const hasTouchScreen =
                'ontouchstart' in window || navigator.maxTouchPoints > 0
            const isMobile =
                /Mobi|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
                    navigator.userAgent
                )
            setIsTouchDevice(hasTouchScreen || isMobile)
        }

        checkTouchDevice()
    }, [])

    return isTouchDevice
}

export default useIsTouchDevice
