import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { ControlPartialWithRelations, DomainPartial, LevelPartial } from '@dinobot/prisma'

interface UpdateControlParams {
    id: string
    data: ControlPartialWithRelations
    level: LevelPartial
    domain: DomainPartial
}

export const useUpdateControl = () => {
    const apiClient = useAuthApiClient()
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: async (params: UpdateControlParams): Promise<ControlPartialWithRelations> => {
            if (!apiClient) throw new Error('API client not available')
            
            const { id, ...rest } = params
            const response = await apiClient.put(`/api/control-mode/${id}`, rest)
            return response.data
        },
        onSuccess: (data, variables) => {
            // Invalidate and refetch controls queries
            queryClient.invalidateQueries({ queryKey: ['controls'] })
            queryClient.invalidateQueries({ queryKey: ['controls-by-class'] })
            queryClient.invalidateQueries({ queryKey: ['control-with-media', variables.id] })
        }
    })
}