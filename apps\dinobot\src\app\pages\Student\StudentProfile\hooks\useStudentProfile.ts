import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useAuthApiClient } from '../../../../contexts/AppContext';
import { useStudentProfileStore } from '../store/StudentProfile.store';
import { User } from '@dinobot/prisma/lib/generated/zod/modelSchema/UserSchema';
import { Level } from '@dinobot/prisma/lib/generated/zod/modelSchema/LevelSchema';

interface UpdateUserRequest {
  id?: string;
  firstName?: string;
  lastName?: string;
  gender?: string;
  levelId?: number;
  birthDate?: string | Date;
}

interface ChangePasswordRequest {
  oldPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}

interface ApiResponse<T = any> {
  type: 'success' | 'error';
  data?: T;
  message?: string;
}

export const useStudentProfile = () => {
  const authApiClient = useAuthApiClient();
  const queryClient = useQueryClient();
  const { setUser } = useStudentProfileStore();

  // Récupération du profil utilisateur
  const userQuery = useQuery({
    queryKey: ['user-profile'],
    queryFn: async (): Promise<{ user: Partial<User> }> => {
      const response = await authApiClient.get('api/auth/me');
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Récupération des levels
  const levelsQuery = useQuery({
    queryKey: ['levels'],
    queryFn: async (): Promise<Level[]> => {
      const response = await authApiClient.get('/api/levels');
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Auto-update du store quand les données arrivent
  useEffect(() => {
    if (userQuery.data) {
      setUser(userQuery.data.user);
    }
  }, [userQuery.data, setUser]);

  // Mutation pour mettre à jour le profil
  const updateProfileMutation = useMutation({
    mutationFn: async (data: Partial<UpdateUserRequest>): Promise<ApiResponse<Partial<User>>> => {
      const payload = {
        firstName: data.firstName,
        lastName: data.lastName,
        gender: data.gender,
        levelId: data.levelId,
        birthDate: data.birthDate
      };

      const response = await authApiClient.put('/api/account', payload);
      return response.data;
    },
    onSuccess: (result) => {
      if (result.type === 'success' && result.data) {
        setUser(result.data);
        queryClient.invalidateQueries({ queryKey: ['user-profile'] });
      }
    }
  });

  // Mutation pour changer le mot de passe
  const changePasswordMutation = useMutation({
    mutationFn: async (data: ChangePasswordRequest): Promise<ApiResponse> => {
      const payload = {
        currentPassword: data.oldPassword,
        newPassword: data.newPassword,
        newPassword2: data.confirmNewPassword
      };

      const response = await authApiClient.post('/api/auth/change-password', payload);
      return response.data;
    }
  });

  // Mutation pour supprimer le compte
  const deleteAccountMutation = useMutation({
    mutationFn: async (): Promise<ApiResponse> => {
      const response = await authApiClient.delete('/api/user/delete');
      return response.data;
    },
    onSuccess: () => {
      // Redirection vers la page de connexion
      window.location.href = '/login';
    }
  });

  return {
    // Queries
    user: userQuery.data,
    isLoadingUser: userQuery.isLoading,
    userError: userQuery.error,

    levels: levelsQuery.data,
    isLoadingLevels: levelsQuery.isLoading,
    levelsError: levelsQuery.error,

    // Mutations
    updateProfile: updateProfileMutation.mutateAsync,
    isUpdatingProfile: updateProfileMutation.isPending,
    updateProfileError: updateProfileMutation.error,

    changePassword: changePasswordMutation.mutateAsync,
    isChangingPassword: changePasswordMutation.isPending,
    changePasswordError: changePasswordMutation.error,

    deleteAccount: deleteAccountMutation.mutateAsync,
    isDeletingAccount: deleteAccountMutation.isPending,
    deleteAccountError: deleteAccountMutation.error,

    // Refetch
    refetchUser: userQuery.refetch,
    refetchLevels: levelsQuery.refetch
  };
};