import { IconInfo } from '@dinobot/components-ui'
import { Label } from '@dinobot/components-ui'
import { RadioGroup, RadioGroupItem } from '@dinobot/components-ui'
import React, { useState } from 'react'
import { TimeSelector } from './time-selector'
import { FormControl, FormField, FormItem } from '@dinobot/components-ui'
import { Control, Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

type SettingsChronoProps = {
    control: Control<any>
}

const SettingsChrono = ({ control }: SettingsChronoProps) => {
    const [checked, setchecked] = useState('retries')
    const { t } = useTranslation('teacher/myClass/evaluation/schedule/settings')
    const [time, settime] = useState({ hours: '00', minutes: '01' })
    // const translate= useTranslation("teacher/scheduleAssessment/param")

    return (
        <div className="flex flex-col gap-2 w-[355.5px] ">
            <h4 className="flex gap-2 items-center">
                {t('title')}
                <IconInfo fill="#5c5c5c" />
            </h4>
            <div className="border rounded-sm p-4">
                <FormField
                    name="timeLimit"
                    control={control}
                    defaultValue={{ hours: '00', minutes: '01' }}
                    render={({ field }) => (
                        <FormItem>
                            <FormControl>
                                <RadioGroup
                                    defaultValue="retries"
                                    value={checked}
                                    onValueChange={e => {
                                        setchecked(e)
                                        if (e === 'no-retries')
                                            field.onChange('')
                                        else field.onChange(time)
                                    }}
                                >
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem
                                            value="no-retries"
                                            id="no-retries"
                                        />
                                        <Label htmlFor="no-retries">
                                            {t('lable_no')}
                                        </Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem
                                            value="retries"
                                            id="retries"
                                        />
                                        <Label htmlFor="retries">
                                            {t('lable_yes')}
                                        </Label>
                                        <TimeSelector
                                            value={time}
                                            onChange={e => {
                                                settime(e)
                                                if (checked === 'retries')
                                                    field.onChange(e)
                                            }}
                                        />
                                    </div>
                                </RadioGroup>
                            </FormControl>
                        </FormItem>
                    )}
                />
            </div>
            <div className="border rounded-sm p-4 ">
                <Controller
                    name="retries"
                    control={control}
                    defaultValue={'false'}
                    render={({ field }) => (
                        <FormItem>
                            <FormControl>
                                <RadioGroup
                                    defaultValue="option-one"
                                    value={field.value}
                                    onValueChange={field.onChange}
                                >
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem
                                            value="false"
                                            id="option-one"
                                        />
                                        <Label htmlFor="option-one">
                                            {t('no_learn')}
                                        </Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem
                                            value="true"
                                            id="option-two"
                                        />
                                        <Label htmlFor="option-two">
                                            {t('autorised_learn')}
                                        </Label>
                                    </div>
                                </RadioGroup>
                            </FormControl>
                        </FormItem>
                    )}
                />
            </div>
        </div>
    )
}

export default SettingsChrono
