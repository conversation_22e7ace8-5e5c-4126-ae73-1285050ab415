'use client'

import * as React from 'react'
import Textarea from 'react-textarea-autosize'
import axios from 'axios'

import { <PERSON>c<PERSON>, Radical, X } from 'lucide-react'
import { Circle } from 'rc-progress'
import { ChatRequestOptions } from 'ai'
import { Button, IconArrowElbow, messageFileType, Tooltip, TooltipContent, TooltipTrigger, TrainTextArea } from '@dinobot/components-ui'
import { fileToBase64 } from '@dinobot/utils'
import { useRecordVoiceStore, useStreamingStore, useTrainCortexStore } from '@dinobot/stores'
import { useEnterSubmit } from '@dinobot/hooks'

export function TrainingPromptForm({
    input,
    setInput,
    imageFile,
    setImageFile,
    fileExtension,
    setFileExtension,
    disabled,
    HandleSubmit,
    status,
    addFileData
}: {
    input: string
    setInput: (
        e:
            | React.ChangeEvent<HTMLInputElement>
            | React.ChangeEvent<HTMLTextAreaElement>
    ) => void
    imageFile: string
    status: 'submitted' | 'streaming' | 'ready' | 'error'
    setImageFile: (value: string) => void
    fileExtension: string
    setFileExtension: (value: string) => void
    addFileData: (data: messageFileType) => void
    HandleSubmit: (
        event?: {
            preventDefault?: () => void
        },
        chatRequestOptions?: ChatRequestOptions
    ) => void
    disabled: boolean
}) {
    const { formRef } = useEnterSubmit()
    const inputRef = React.useRef<HTMLTextAreaElement>(null)
    const [selectedFile, setSelectedFile] = React.useState<any>(null)
    const [imageHovered, setImageHovered] = React.useState(false)
    const fileInputRef = React.useRef<HTMLInputElement>(null)
    const { openCortexDialog, reset } = useTrainCortexStore()

    /*   const [flag, setFlag] = useState(true);

  React.useEffect(() => {
    const interval = setInterval(() => {
      setFlag(prevFlag => !prevFlag);
    }, 1000);

    return () => clearInterval(interval); 
  }, []); */

    const [percentage, setPercentage] = React.useState<number>(0)
    const [isUploading, setIsUploading] = React.useState<boolean>(false)

    const { setIsStreaming } = useStreamingStore()

    const {
        recordedAudio,
        clearRecordedAudio,
        recording,
        transcriptionResult
    } = useRecordVoiceStore()
    //const [ recording, setRecording ] = useState<boolean>(false);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0]
        //console.log("file's name is : " + file?.name);
        if (file) {
            // console.log("Before set selected image")
            setSelectedFile({
                name: file.name,
                file: URL.createObjectURL(file)
            })
            handleUpload(file)
        }
    }

    const handleUpload = async (file: File) => {
        setIsUploading(true)
        const formData = new FormData()
        formData.append('image', file)

        try {
            const response = await axios.post('/api/uploadImage', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                },
                onUploadProgress: (progressEvent: any) => {
                    const progress = Math.round(
                        (progressEvent.loaded / progressEvent.total) * 100
                    )
                    setPercentage(progress)
                }
            })

            if (response.status !== 200) {
                throw new Error('Failed to upload image')
            }

            const { originalName, extension } = response.data
            setInput({
                target: { value: input }
            } as React.ChangeEvent<HTMLInputElement>)
            setImageFile(originalName)
            setFileExtension(extension)
            addFileData({
                id: Date.now().toString(),
                name: `${originalName}-${Date.now()}.${extension || 'png'}`,
                file: `data:${file.type};base64,${await fileToBase64(file)}`,
                userId: '',
                messageId: '',
                createdAt: new Date()
            })
        } catch (error) {
            // Gérer l'erreur silencieusement
        } finally {
            setIsUploading(false)
        }
    }

    const handleButtonClick = () => {
        if (fileInputRef.current) {
            fileInputRef.current.click()
        }
    }

    React.useEffect(() => {
        if (inputRef.current) {
            inputRef.current.focus()
        }
    }, [])

    const examDisableCondition = false //feature === "Exam" ? (exercise ? false : true) : false

    return (
        <form
            className="flex flex-col items-center"
            ref={formRef}
            onSubmit={async (e: any) => {
                e.preventDefault()
                //console.log("We submit something...")
                HandleSubmit()
                setIsStreaming(true)
                setSelectedFile(null)
                // Blur focus on mobile
                if (window.innerWidth < 600) {
                    e.target['message']?.blur()
                }

                const value = input.trim()
                setInput({
                    target: { value: '' }
                } as React.ChangeEvent<HTMLInputElement>)
                reset()
                //clearRecordedAudio()
                if (!value && !imageFile && !transcriptionResult) return

                // Optimistically add user message UI is now handled by useChat

                clearRecordedAudio()

                // No need to manually handle response message anymore
                setImageFile('')
                setSelectedFile(null)
            }}
        >
            <div className="relative flex max-h-40 w-full grow flex-col bg-background sm:rounded-md sm:border mt-2">
                <input
                    type="file"
                    accept="image/*,.pdf"
                    onChange={handleFileChange}
                    ref={fileInputRef}
                    style={{ display: 'none' }}
                    onClick={e => e.stopPropagation()}
                />

                {/*             <div className={`${disabled ? "cursor-not-allowed" : ""} absolute right-10 top-4 size-9 rounded-full p-0 sm:right-28 border transition-all duration-500`}>
              <Microphone />
            </div> */}

                {!recordedAudio
                    ? null
                    : /*                 <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    type='button'
                    onClick={() => {
                      if (!recording) {
                        startRecording()
                      }
                      if (recording) {
                        stopRecording()
                      }
                    }}    
                    disabled={disabled || examDisableCondition}
                    className={`${disabled ? "cursor-not-allowed" : ""} absolute right-20 top-4 size-9 bg-background p-0 sm:right-28 rounded-full ${recording ? " text-dinoBotRed hover:bg-dinoBotRed hover:text-white" : " text-dinoBotBlue hover:bg-dinoBotBlue hover:text-white" } transition-all duration-500 animate-fade-in-down`}
                  >
                    <Mic />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className={`${recording ? "bg-dinoBotRed" : "bg-dinoBotBlue"}`}>Parles à DinoBot</TooltipContent>
              </Tooltip> */
                      null}

                {recording
                    ? null /*<div className='text-white bg-dinoBotRed rounded-xl p-1 text-sm font-bold absolute right-10 top-[-20px] sm:right-16 animate-pulse'>Enregistrement...</div> */
                    : null}

                {/* Might use this package for a better implementation later off : https://www.npmjs.com/package/react-voice-visualizer */}

                {recordedAudio
                    ? null
                    : /*             <div className={`absolute right-10 ${selectedFile ? "top-[-170px]" : "top-[-60px]"}  flex flex-row items-center bg-white px-2 rounded-xl border border-dinoBotBlue ${!isProcessing ? "" : "animate-pulse"}`}>
              <AudioPlayer
                src={recordedAudio}
                minimal={true}
                width={250}
                trackHeight={40}
                barWidth={3}
                gap={1}
                visualise={true}
                backgroundColor="#FFFFFF"
                barColor="#000000"
                barPlayedColor="#4a70cb"
                seekBarColor='#4a70cb'
                skipDuration={2}
                showLoopOption={true}
                showVolumeControl={true}
                hideSeekBar={true}
                hideSeekKnobWhenPlaying={false}
                //volumeControlColor='#FFFFFF'
              />
              <Tooltip>
                <TooltipTrigger asChild>
                  <CircleX onClick={clearRecordedAudio} className='text-black ml-2 cursor-pointer hover:text-dinoBotBlue' />
                </TooltipTrigger>
                <TooltipContent className={`bg-dinoBotBlue`}>Enlever le message vocal</TooltipContent>
              </Tooltip>
            </div> */

                      null}

                {selectedFile ? (
                    <div className="relative bottom-0">
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div
                                    onClick={() => {
                                        setSelectedFile(null)
                                        setImageFile('')
                                        setSelectedFile(null)
                                    }}
                                    onMouseEnter={() => setImageHovered(true)}
                                    onMouseLeave={() => setImageHovered(false)}
                                    className="flex items-center justify-center absolute right-10 bottom-0 size-20 p-0 sm:right-16 cursor-pointer border rounded-md z-20"
                                >
                                    <div className="absolute top-[-36%] w-28 text-xs flex flex-row bg-white border rounded-md p-1">
                                        <div className="truncate">
                                            {imageFile.replace(/\.[^/.]+$/, '')}
                                        </div>
                                        .{fileExtension}
                                    </div>
                                    <div
                                        className={`absolute right-0 bottom-0 bg-white ${
                                            imageHovered
                                                ? 'opacity-50'
                                                : 'opacity-30'
                                        } size-full transition-all duration-500`}
                                    ></div>
                                    {isUploading ? (
                                        <Circle
                                            className="absolute right-0 bottom-0"
                                            percent={percentage}
                                            strokeWidth={8}
                                            strokeColor="#00bd95"
                                        />
                                    ) : null}
                                    <div
                                        className={`flex items-center justify-center size-10 ${
                                            imageHovered
                                                ? 'opacity-100'
                                                : 'opacity-80'
                                        } text-dinoBotCyan rounded-full border border-dinoBotCyan z-20 transition-all duration-500`}
                                    >
                                        <X size={30} />
                                    </div>
                                </div>
                            </TooltipTrigger>
                            <TooltipContent className="bg-dinoBotCyan">
                                {selectedFile.name.endsWith('pdf')
                                    ? 'Enlever le PDF'
                                    : "Enlever l'image"}
                            </TooltipContent>
                        </Tooltip>

                        {selectedFile.name.endsWith('pdf') ? (
                            <img
                                className={`absolute right-10 bottom-0 size-20 bg-background p-0 sm:right-16 border rounded-md z-10 object-cover`}
                                src='/pdf-logo.jpg'
                                width={80}
                                height={80}
                                alt="Votre fichier PDF"
                            />
                        ) : (
                            <img
                                className="absolute right-10 bottom-0 size-20 bg-background p-0 sm:right-16 border rounded-md z-10 object-cover"
                                src={selectedFile.file}
                                width={80}
                                height={80}
                                alt="Votre imge"
                            />
                        )}
                    </div>
                ) : (
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                type="button"
                                //variant="outline"
                                size="icon"
                                disabled={disabled || examDisableCondition}
                                className={`${disabled ? 'cursor-not-allowed' : ''} z-10 absolute right-10 top-1/2 -translate-y-1/2 size-8 rounded-full bg-background p-0 sm:right-14 text-dinoBotCyan hover:text-white dark:hover:text-gray-50 hover:bg-dinoBotCyan hover:border-dinoBotCyan hover:border transition-all duration-500`}
                                onClick={handleButtonClick}
                            >
                                {/* <ImagePlus className="" /> */}
                                <Paperclip />
                                <span className="sr-only">
                                    Ajouter un fichier Image ou PDF
                                </span>
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent className="bg-dinoBotCyan">
                            Ajouter un fichier Image ou PDF
                        </TooltipContent>
                    </Tooltip>
                )}
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            type="button"
                            //variant="outline"
                            size="icon"
                            disabled={disabled || examDisableCondition}
                            className={`${disabled ? 'cursor-not-allowed' : ''} z-10  absolute right-24 top-1/2 -translate-y-1/2 size-8 rounded-full bg-background p-0 sm:right-24 text-dinoBotRed hover:text-white dark:hover:text-gray-50 hover:bg-dinoBotRed hover:border-dinoBotRed hover:border transition-all duration-500`}
                            onClick={openCortexDialog}
                        >
                            {/* <ImagePlus className="" /> */}
                            <Radical />
                            <span className="sr-only">
                                Ajouter une formule mathématique
                            </span>
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent className="bg-dinoBotRed">
                        Ajouter une formule mathématique
                    </TooltipContent>
                </Tooltip>

                <Textarea
                    ref={inputRef}
                    tabIndex={0}
                    //onKeyDown={onKeyDown}
                    placeholder={
                        disabled
                            ? 'Connecte-toi pour continuer à utiliser DinoBot !'
                            : 'Envoi un message...'
                    }
                    //className={`${disabled ? `cursor-not-allowed placeholder:text-dinoBotRed placeholder:font-bold ${flag ? "animate-moveAround duration-500" : "animate-bounce duration-500"} ` : ""} min-h-[60px] w-full resize-none bg-transparent pl-4 pr-[150px] py-[1.3rem] focus-within:outline-none sm:text-sm`}
                    className={`hidden ${disabled ? `cursor-not-allowed placeholder:text-dinoBotRed/50 placeholder:font-bold` : ''} min-h-[60px] w-full resize-none bg-transparent pl-4 pr-[150px] py-[1.3rem] focus-within:outline-none sm:text-sm`}
                    autoFocus
                    spellCheck={false}
                    autoComplete="off"
                    autoCorrect="off"
                    name="message"
                    rows={1}
                    value={input}
                    onChange={e => setInput(e)}
                    disabled={disabled || examDisableCondition}
                />

                <TrainTextArea
                    placeholder={
                        disabled
                            ? 'Connecte-toi pour continuer à utiliser DinoBot !'
                            : 'Envoi un message...'
                    }
                    onChange={value =>
                        setInput({
                            target: { value }
                        } as React.ChangeEvent<HTMLInputElement>)
                    }
                    theme={'snow'}
                    className={`  border-none ${disabled ? ` cursor-not-allowed placeholder:text-dinoBotRed/50 placeholder:font-bold` : ''}  w-4/5 resize-none bg-transparent  focus-within:outline-none sm:text-sm`}
                />

                <div
                    className={`${disabled ? 'cursor-not-allowed' : ''} absolute right-0 top-1/2 -translate-y-1/2 sm:right-1`}
                >
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Button
                                className={` bg-dinoBotBlue rounded-xl hover:bg-white hover:text-dinoBotBlue hover:border-dinoBotBlue hover:border`}
                                type="submit"
                                size="icon"
                                disabled={
                                    (input.trim() === '' &&
                                        transcriptionResult.trim() === '') ||
                                    disabled ||
                                    status === 'streaming' ||
                                    examDisableCondition
                                }
                            >
                                <IconArrowElbow />
                                <span className="sr-only">
                                    Envoyer un message
                                </span>
                            </Button>
                        </TooltipTrigger>
                        <TooltipContent className="bg-dinoBotBlue">
                            Envoyer un message
                        </TooltipContent>
                    </Tooltip>
                </div>
            </div>
        </form>
    )
}
