# Docker Deployment Guide for Dinobot Monorepo

## Overview
This guide outlines the steps to create a single Dockerfile in the root directory that handles both the frontend (React/Vite) and backend (Hono.js) applications using Nx build system for production deployment.

## Current Architecture
- **Frontend**: React app with Vite (`apps/dinobot/`) - Port 3100 (dev), 4100 (preview)
- **Backend**: Hono.js API server (`apps/dinobot-backend/`) - Port 3001
- **Build System**: Nx monorepo with shared libraries

## Deployment Strategy

### Option 1: Single Container with Both Services (Recommended for Development)
Create a single Docker container that runs both frontend and backend services using a process manager like PM2.

### Option 2: Multi-Stage Build with Nginx Proxy (Recommended for Production)
Build both applications and serve the frontend through Nginx while proxying API calls to the backend.

## Implementation Steps

### Step 1: Create Root Dockerfile

```dockerfile
# Multi-stage build for production deployment
FROM node:18-alpine AS base

# Install pnpm
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY nx.json ./
COPY tsconfig.base.json ./

# Copy all app and lib package.json files
COPY apps/dinobot/package.json ./apps/dinobot/
COPY apps/dinobot-backend/package.json ./apps/dinobot-backend/
COPY libs/*/package.json ./libs/*/

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build stage
FROM base AS builder

# Generate Prisma client and build all libraries
RUN pnpm nx run @dinobot/prisma:build
RUN pnpm nx run-many --target=build --projects=@dinobot/utils,@dinobot/stores,@dinobot/hooks,@dinobot/components-ui

# Build backend
RUN pnpm nx build dinobot-backend

# Build frontend for production
RUN pnpm nx build dinobot

# Production stage
FROM node:18-alpine AS production

# Install pnpm and PM2
RUN npm install -g pnpm pm2

WORKDIR /app

# Copy built applications
COPY --from=builder /app/dist/apps/dinobot-backend ./backend
COPY --from=builder /app/dist/apps/dinobot ./frontend

# Copy package files for production dependencies
COPY --from=builder /app/package.json ./
COPY --from=builder /app/pnpm-lock.yaml ./

# Install only production dependencies
RUN pnpm install --prod --frozen-lockfile

# Copy PM2 ecosystem file
COPY ecosystem.config.js ./

# Expose ports
EXPOSE 3001 3000

# Start both services using PM2
CMD ["pm2-runtime", "start", "ecosystem.config.js"]
```

### Step 2: Create PM2 Ecosystem Configuration

Create `ecosystem.config.js` in the root:

```javascript
module.exports = {
  apps: [
    {
      name: 'dinobot-backend',
      script: './backend/main.js',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      }
    },
    {
      name: 'dinobot-frontend',
      script: 'serve',
      args: '-s ./frontend -l 3000',
      env: {
        NODE_ENV: 'production'
      }
    }
  ]
};
```

### Step 3: Alternative Nginx-Based Approach

Create `Dockerfile.nginx`:

```dockerfile
FROM node:18-alpine AS builder

# Build steps same as above...

FROM nginx:alpine AS production

# Install Node.js for backend
RUN apk add --no-cache nodejs npm

# Install pnpm and PM2
RUN npm install -g pnpm pm2

# Copy built frontend to nginx
COPY --from=builder /app/dist/apps/dinobot /usr/share/nginx/html

# Copy backend
COPY --from=builder /app/dist/apps/dinobot-backend /app/backend

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy startup script
COPY start.sh /start.sh
RUN chmod +x /start.sh

EXPOSE 80

CMD ["/start.sh"]
```

Create `nginx.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    upstream backend {
        server localhost:3001;
    }

    server {
        listen 80;
        server_name localhost;

        # Serve frontend
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
        }

        # Proxy API calls to backend
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket support if needed
        location /ws {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
```

Create `start.sh`:

```bash
#!/bin/sh

# Start backend in background
cd /app && pm2 start backend/main.js --name backend --no-daemon &

# Start nginx in foreground
nginx -g 'daemon off;'
```

### Step 4: Update Nx Build Configuration

Ensure production builds are optimized. Update `apps/dinobot/project.json`:

```json
{
  "targets": {
    "build": {
      "executor": "@nx/vite:build",
      "options": {
        "outputPath": "dist/apps/dinobot"
      },
      "configurations": {
        "production": {
          "mode": "production",
          "sourcemap": false,
          "minify": true
        }
      }
    }
  }
}
```

### Step 5: Environment Variables

Create `.env.production`:

```bash
# Database
DATABASE_URL="**********************************/dinobot_prod"

# Backend Configuration
NODE_ENV=production
PORT=3001
BACKEND_URL="http://localhost:3001"
FRONTEND_URL="http://localhost:3000"

# Add other production environment variables...
```

### Step 6: Docker Compose for Complete Stack

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"  # Frontend
      - "3001:3001"  # Backend API
    environment:
      - NODE_ENV=production
      - DATABASE_URL=**************************************/dinobot_prod
    depends_on:
      - db
    volumes:
      - ./uploads:/app/uploads

  db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: dinobot_prod
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

## Build and Deployment Commands

### Development Testing
```bash
# Test the build locally
docker build -t dinobot-app .
docker run -p 3000:3000 -p 3001:3001 dinobot-app
```

### Production Deployment
```bash
# Build and deploy with compose
docker-compose up --build -d

# Or build individually
docker build -f Dockerfile.nginx -t dinobot-app .
docker run -p 80:80 dinobot-app
```

### Nx Commands for Verification
```bash
# Ensure all builds work before Docker
nx run-many --target=build --all
nx build dinobot --configuration=production
nx build dinobot-backend --configuration=production
```

## Optimization Considerations

1. **Multi-stage builds** to reduce final image size
2. **Layer caching** by copying package.json files first
3. **Production dependencies only** in final stage
4. **Static asset optimization** with proper compression
5. **Health checks** for container orchestration
6. **Proper logging** configuration for production monitoring

## Next Steps

1. Create the Dockerfile using Option 1 or 2
2. Test locally with sample data
3. Configure environment variables for production
4. Set up CI/CD pipeline for automated builds
5. Deploy to container orchestration platform (Docker Swarm, Kubernetes, etc.)

## Security Considerations

- Use non-root user in container
- Implement proper secrets management
- Configure CORS properly for production domains
- Set up SSL/TLS termination
- Implement rate limiting and security headers