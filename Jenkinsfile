def slackStatus = 'STARTED'
def frontendDockerImage
def backendDockerImage
def branchName
def version
def committer
def config = [
    servers: [:],
    docker: [:],
    nexus: [:]
]

node {
    properties([
        buildDiscarder(logRotator(numToKeepStr: '10')),
    ])

    stage('Configuration') {
        script {
            env.NODE_HOME = tool name: 'techin_node_18'
            env.PATH = "${env.NODE_HOME}/bin:${env.PATH}"
            sh 'node -v'
            sh 'npm -v'
            sh "docker --version"
            sh "git --version"
            checkout scm
            branchName = env.BRANCH_NAME
            def packageJson = readJSON file: 'package.json'
            version = packageJson.version
            committer = sh(script: 'git log -1 --pretty=format:"%an"', returnStdout: true).trim()
        }
    }

    try {
        stage('Initialization') {
            script {
                withVault(
                configuration: [
                    vaultCredentialId  : 'vault-access',
                    vaultUrl           : 'https://boualem.tech-instinct.com/',
                    timeout            : 60,
                    skipSslVerification: true,
                    engineVersion      : 2
                ],
                vaultSecrets: [
                    [
                    path        : 'cicd-secret/servers/dinobot/dev-server',
                    secretValues: [
                        [envVar: 'DEV_NAME', vaultKey: 'ssh-name'],
                    ]
                    ],
                    [
                    path        : 'cicd-secret/servers/dinobot/prod-server',
                    secretValues: [
                        [envVar: 'PROD_NAME', vaultKey: 'ssh-name'],
                    ]
                    ],
                    [
                    path        : 'cicd-secret/nexus/credentials',
                    secretValues: [
                        [envVar: 'NEXUS_HOST', vaultKey: 'registry_url'],
                        [envVar: 'NEXUS_USER', vaultKey: 'username'],
                        [envVar: 'NEXUS_PASSWORD', vaultKey: 'password']
                    ]
                    ]
                ]
                ) {
                // servers information
                config.servers = [
                    dev : [name: 'dev', host: "${DEV_NAME}"],
                    prod: [name: 'prod', host: "${PROD_NAME}"]
                ]
                // nexus information
                config.nexus = [
                    host    : "${NEXUS_HOST}",
                    user    : "${NEXUS_USER}",
                    password: "${NEXUS_PASSWORD}"
                ]
                // images information
                config.docker = [
                    dev : [
                        frontend: "${config.nexus.host}/dinobot-frontend-dev",
                        backend: "${config.nexus.host}/dinobot-backend-dev"
                    ],
                    prod: [
                        frontend: "${config.nexus.host}/dinobot-frontend-prod",
                        backend: "${config.nexus.host}/dinobot-backend-prod"
                    ]
                ]
                }
            }
        }
        
        stage("SonarQube analysis") {
            // if(env.BRANCH_NAME == 'dev-dep') {
            //     sh "corepack enable"
            //     sh "pnpm install"
            //     sh "nx run-many --target=build --all"
            //     withSonarQubeEnv("techinSonar") {
            //         sh """
            //             sonar-scanner \
            //             -Dsonar.projectBaseDir=. \
            //             -Dsonar.login=${env.SONAR_AUTH_TOKEN}
            //         """
            //     }
            // }
        }

        stage("Quality Gate") {
            // if(env.BRANCH_NAME == 'x') {
            //     timeout(time: 1, unit: 'HOURS') {
            //         def qg = waitForQualityGate()
            //         if (qg.status != 'OK') {
            //             error "Pipeline aborted due to quality gate failure: ${qg.status}"
            //         }
            //     }
            // }
        }
        
        stage("Build Dependencies") {
            sh "docker build --target deps -t dinobot-deps -f apps/dinobot/Dockerfile ."
            sh "docker build --target deps -t dinobot-backend-deps -f apps/dinobot-backend/Dockerfile ."
        }

        stage("Build Applications") {
            sh "docker build --target builder -t dinobot-frontend-builder -f apps/dinobot/Dockerfile ."
            sh "docker build --target builder -t dinobot-backend-builder -f apps/dinobot-backend/Dockerfile ."
        }

        stage("Build & Publish") {
            
            if(branchName == 'dev-dep') {
                frontendDockerImage = config.docker.dev.frontend
                backendDockerImage = config.docker.dev.backend
            }
            if(branchName == 'release') {
                frontendDockerImage = config.docker.staging.frontend
                backendDockerImage = config.docker.staging.backend
            }
            if(branchName == 'main') {
                frontendDockerImage = config.docker.prod.frontend
                backendDockerImage = config.docker.prod.backend
            }
            
            sh """#!/bin/bash
                    echo "${config.nexus.password}" | docker login -u ${config.nexus.user} --password-stdin "${config.nexus.host}"
            """
            
            // Build and push frontend
            sh """
                docker build --target runner -t ${frontendDockerImage}:${version} -t ${frontendDockerImage}:latest -f apps/dinobot/Dockerfile .
                docker push ${frontendDockerImage}:${version}
                docker push ${frontendDockerImage}:latest
            """
            
            // Build and push backend
            sh """
                docker build --target runner -t ${backendDockerImage}:${version} -t ${backendDockerImage}:latest -f apps/dinobot-backend/Dockerfile .
                docker push ${backendDockerImage}:${version}
                docker push ${backendDockerImage}:latest
            """
            
            sh "docker logout ${config.nexus.host}"
        }

        stage("Restart in server") {
            if(branchName == 'dev-dep') {
                def serverConfig = config.servers.dev
                sh """
                    set -e
                    ssh -o StrictHostKeyChecking=no ${serverConfig.host} '
                        set -e
                        cd dinobot-env || exit 1
                        docker compose pull dinobot-frontend-dev dinobot-backend-dev || exit 1
                        docker compose up -d dinobot-frontend-dev dinobot-backend-dev || exit 1
                        docker image prune -a -f || exit 1
                    '
                """
            }

            if(branchName == 'main') {
                def serverConfig = config.servers.prod
                sh """
                    set -e
                    ssh -o StrictHostKeyChecking=no ${serverConfig.host} '
                        set -e
                        cd dinobot-env || exit 1
                        docker compose pull dinobot-frontend-prod dinobot-backend-prod || exit 1
                        docker compose up -d dinobot-frontend-prod dinobot-backend-prod || exit 1
                        docker image prune -a -f || exit 1
                    '
                """
            }
        }

        stage("increment version") {
            if(env.BRANCH_NAME == 'dev-dep'){
                withCredentials([sshUserPrivateKey(credentialsId: 'ti-jenkins-git', keyFileVariable: 'SSH_KEY')]) {
                    sh "git config user.email 'Jenkins@tech-instinct'"
                    sh "git config user.name 'Jenkins'"
                    sh 'GIT_SSH_COMMAND="ssh -i $SSH_KEY" git fetch --all'
                    sh "git checkout develop"
                    sh 'GIT_SSH_COMMAND="ssh -i $SSH_KEY" git pull origin develop --rebase'

                    def oldVersion = version
                    sh "npm version patch --no-git-tag-version"
                    def newPackageJson = readJSON file: 'package.json'
                    def newVersion = newPackageJson.version

                    sh "git add package.json"
                    sh "git commit -m 'ci(package): increment package version to ${newVersion}'"
                    sh 'GIT_SSH_COMMAND="ssh -i $SSH_KEY" git push origin develop'
                }
            }
        }
    } catch (err) {
        slackStatus = "FAILED"
        throw err;
    } finally {

        try {
            removeDockerImages("dinobot-deps")
            removeDockerImages("dinobot-backend-deps")
            removeDockerImages("dinobot-frontend-builder")
            removeDockerImages("dinobot-backend-builder")
            removeDockerImages("${frontendDockerImage}:${version}")
            removeDockerImages("${frontendDockerImage}:latest")
            removeDockerImages("${backendDockerImage}:${version}")
            removeDockerImages("${backendDockerImage}:latest")
        } catch (Exception e) {
            echo "Exception while removing docker images: ${e}"
        }
        
        if(slackStatus != 'FAILED') slackStatus = 'SUCCESSFUL'
        notifyBuild(slackStatus, committer, branchName)
    }


}

def removeDockerImages(String imageName) {
    echo "Deleting docker image: ${imageName}"
    sh "docker rmi ${imageName}"
}

def notifyBuild(String buildStatus, String committer, String branchName) {
    def slackColor = '#9b9b9b'
    if(buildStatus == 'STARTED') {
        slackColor = '#f5a623'
    }
    if(buildStatus == 'SUCCESSFUL') {
        slackColor = '#83ef9b'
    }
    if(buildStatus == 'FAILED') {
        slackColor = '#f09696'
    }

    def attachment = [
        title: "[${env.BUILD_NUMBER}] DINOBOT ${buildStatus}",
        text: "pushed by ${committer} \n on branch ${branchName}",
        color: slackColor,
        footer: "Tech-Instinct",
        ts: System.currentTimeMillis() / 1000 as int,
        actions: [
            [
                type: "button",
                text: "Open Build Report",
                url: env.BUILD_URL
            ]
        ]
    ]

    slackSend(
        channel: "#dinobot",
        attachments: [attachment]
    )
}