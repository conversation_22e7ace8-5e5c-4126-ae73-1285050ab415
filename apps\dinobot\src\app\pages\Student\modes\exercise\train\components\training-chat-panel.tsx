import * as React from 'react'

//import { ChatShareDialog } from '@/components/chat-share-dialog'
import Cookies from 'js-cookie'
import { TrainingPromptForm } from './training-prompt-form'
import { ChatRequestOptions } from 'ai'
import { Session } from '@dinobot/utils'
import { ButtonScrollToBottom, FooterText, messageFileType } from '@dinobot/components-ui'

export interface TrainingChatPanelProps {
    input: string
    setInput: (
        e:
            | React.ChangeEvent<HTMLInputElement>
            | React.ChangeEvent<HTMLTextAreaElement>
    ) => void
    imageFile: string
    setImageFile: (value: string) => void
    fileExtension: string
    setFileExtension: (value: string) => void
    isAtBottom: boolean
    status: 'submitted' | 'streaming' | 'ready' | 'error'
    scrollToBottom: () => void
    session?: Session
    addFileData: (data: messageFileType) => void
    handleSubmit: (
        event?: {
            preventDefault?: () => void
        },
        chatRequestOptions?: ChatRequestOptions
    ) => void
}

export function TrainingChatPanel({
    input,
    setInput,
    imageFile,
    setImageFile,
    fileExtension,
    setFileExtension,
    isAtBottom,
    scrollToBottom,
    addFileData,
    handleSubmit,
    status,
    session
}: TrainingChatPanelProps) {

    const restrictFreeUser = Cookies.get('restrictFreeUser')

    return (
        <div className="w-full bg-gradient-to-b from-muted/30 from-0% to-muted/30 to-50% duration-300 ease-in-out animate-in dark:from-background/10 dark:from-10% dark:to-background/80">
            <ButtonScrollToBottom
                isAtBottom={isAtBottom}
                scrollToBottom={scrollToBottom}
            />

            <div className="mx-auto sm:max-w-2xl sm:px-4">
                <div className="mx-4 border-t rounded-md bg-background px-2 shadow-lg sm:rounded-t-xl sm:border md:pb-2 sm:mx-0">
                    <TrainingPromptForm
                        disabled={
                            !session?.user && restrictFreeUser === 'yes'
                                ? true
                                : false
                        }
                        input={input}
                        setInput={setInput}
                        imageFile={imageFile}
                        setImageFile={setImageFile}
                        fileExtension={fileExtension}
                        setFileExtension={setFileExtension}
                        status={status}
                        addFileData={addFileData}
                        HandleSubmit={handleSubmit}
                    />
                    <FooterText className="hidden sm:block" />
                </div>
            </div>
        </div>
    )
}
