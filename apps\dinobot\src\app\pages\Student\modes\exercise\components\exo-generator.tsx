//Hooks
import React, { useEffect, useState, useCallback } from 'react'
import Cookies from 'js-cookie'

//Ccmponents
import ExoFromDb from './tabs/exo-from-db/exo-from-db'
import ExoFromFile from './tabs/exo-from-file/exo-from-file'
import ExoFromExam from './tabs/exo-from-exam/exo-from-exam'
import BetaPopup from './tabs/beta-popup'

//Icons
import { BookOpenText, ChevronLeft, Database } from 'lucide-react'

//UI

//Store
import { useLocalStorageStore,selectUseExoModeStore ,useAccountStore} from '@dinobot/stores'

//Prisma-Schema
import { FeatureFlagName } from '@prisma/client'
import { Level ,Domain,ExamTypeType} from '@dinobot/prisma'


//API-function-call
import { useExerciseData } from '../hooks'
import { toast } from 'sonner'
import { useTranslation } from 'react-i18next'
import { But<PERSON>, UploadFile } from '@dinobot/components-ui'
import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../../contexts/AppContext'

// interface ExoGeneratorProps {
//     getDomains: (
//         level: string
//     ) => Promise<Domain[] | { error: string; status: number }>
//     getChapters: (domain: string, level: string) => Promise<Chapter[]>
//     getParts: (chapterId: string) => Promise<Part[]>
//     getSubjectsByDomainIdAndLevelId: (
//         domainId: number,
//         levelId: number | null
//     ) => Promise<ExoSubject[]>
// }

function ExoGenerator() {
    const [activeTab, setActiveTab] = useState('')
    const [isExamMode, setIsExamMode] = useState<boolean | null>()
    const [domains, setDomains] = useState<Domain[]>([])
    const setSubject = selectUseExoModeStore.use.setSubject()
    const setLevel = selectUseExoModeStore.use.setLevel()
    const setMode = selectUseExoModeStore.use.setMode()
    const { user } = useAccountStore()
    const { t } = useTranslation(['app/mode'],{keyPrefix: 'exo.tab'})
    const [examType, setExamType] = React.useState<ExamTypeType[]>([])
    const domainId = useLocalStorageStore(state => state.domainId)
    const topicId = Cookies.get('topicId')
    const session = JSON.parse(Cookies.get('session') || '{}')
    const userLevelId = session?.user?.userLevel?.id
    const authApiClient = useAuthApiClient()
    
    // Use hooks for API calls
    const { useDomainsByLevel, useLevels } = useExerciseData()

    // Get levels data using hook
    const { data: levelsData } = useLevels()
    
    // Get domains data using hook
    const { data: domainsData } = useDomainsByLevel(userLevelId)

    // Get exam types by level and domain
    const { data: examTypesData } = useQuery({
        queryKey: ['examTypes', userLevelId, domainId],
        queryFn: async (): Promise<ExamTypeType[]> => {
            const response = await authApiClient.get(`/api/domain-level/exam-types/${userLevelId}/${domainId}`);
            return response.data || [];
        },
        enabled: !!(domainId && userLevelId),
        staleTime: 5 * 60 * 1000,
    })

    useEffect(() => {
        if (user?.userLevel) {
            setLevel(user.userLevel as Level)
        }
    }, [user?.userLevel, setLevel])

    useEffect(() => {
        if (topicId) {
            setSubject(topicId)
        }
    }, [topicId, setSubject])

    useEffect(() => {
        if (domainsData) {
            setDomains(domainsData as Domain[])
        }
    }, [domainsData])

    useEffect(() => {
        if (examTypesData) {
            setExamType(examTypesData)
        }
    }, [examTypesData])
    //         setDomains(domains)
    //     })()
    // }, [levelIdFromCookie])

    useEffect(() => {
        switch (activeTab) {
            case 'DB':
                setMode('FROM_DB')
                break
            case 'FILE':
                setMode('FROM_FILE')
                break
            case 'EXAM':
                setMode('FROM_EXAM')
                break
        }
    }, [activeTab, setMode])

    // GetExam - Implemented with React Query hook above
    useEffect(() => {
        if (domainId && userLevelId && examTypesData) {
            setExamType(examTypesData)
        }
    }, [domainId, userLevelId, examTypesData])

    const handleTabClick = (tabName: string) => {
        setActiveTab(tabName)
    }

    const handleBackClick = () => {
        setActiveTab('')
    }

    // Tab content mapping
    const tabContents = {
        DB: (
            <ExoFromDb
                domains={domains}
                // getChapters={getChapters}
                // getParts={getParts}
            />
        ),
        FILE: <ExoFromFile />,
        EXAM: (
            <ExoFromExam
                domains={domains}
                // getSubjectsByDomainIdAndLevelId={
                //     getSubjectsByDomainIdAndLevelId
                // }
            />
        )
    }

    return (
        <div className="w-full h-auto relative flex flex-col gap-12 mb-3 items-center">
            <h2 className="text-2xl md:text-3xl text-dinoBotVibrantBlue leading-9 font-extrabold text-center underline 2xl:mb-5">
                {t('title')}
            </h2>

            {/* Back button positioned to the left outside of the bordered div */}
            <div className="w-full flex items-start gap-4">
                {activeTab !== '' && (
                    <Button
                        onClick={handleBackClick}
                        className="px-4 py-2 bg-dinoBotBlue hover:bg-dinoBotBlue rounded-full"
                        size="icon"
                    >
                        <ChevronLeft
                            className="text-white !w-8 !h-8"
                            strokeWidth={2.5}
                        />
                    </Button>
                )}

                {/* Show tab buttons when no tab is selected */}
                <div
                    className={`border border-dinoBotVibrantBlue bg-white w-[850px] ${activeTab === '' ? 'flex items-center justify-center xl:h-[400px] 2xl:h-[550px]' : ''}`}
                >
                    {activeTab === '' && (
                        <div>
                            <h2 className="text-dinoBotGray text-lg font-bold text-center mb-8">
                                {t('secondTitle')}
                            </h2>
                            <div className="flex flex-row gap-4 justify-around items-center border-2 border-dinoBotGray/60 rounded-lg">
                                <div
                                    className={`flex flex-col items-center p-4 cursor-pointer`}
                                    onClick={() => handleTabClick('DB')}
                                >
                                    <Database
                                        className={`w-14 h-14 mb-2 bg-dinoBotLightBlue rounded-full p-3 text-black`}
                                    />
                                    <span className="text-sm font-medium text-gray-700 text-center leading-tight">
                                        <p className="font-medium">
                                            {t('db.title')}
                                        </p>
                                    </span>
                                </div>

                                <BetaPopup feature="FILE">
                                    <div
                                        className={`flex flex-col items-center p-4`}
                                        onClick={() => handleTabClick('FILE')}
                                    >
                                        <UploadFile
                                            className={`size-14 mb-2 bg-dinoBotLightBlue rounded-full p-[10px] overflow-visible text-black`}
                                        />
                                        <span className="text-sm font-medium text-gray-700 text-center leading-tight">
                                            <p className="font-medium">
                                                {t('file.title')}
                                            </p>
                                        </span>
                                    </div>
                                </BetaPopup>
                                {isExamMode &&
                                    (examType && examType[0] ? (
                                        <BetaPopup feature="EXAM">
                                            <div
                                                onClick={() =>
                                                    handleTabClick('EXAM')
                                                }
                                                className={`flex flex-col items-center p-4`}
                                            >
                                                <BookOpenText
                                                    className={`w-14 h-14 mb-2 bg-dinoBotLightBlue overflow-visible rounded-full p-3 text-black`}
                                                />
                                                <span className="text-sm font-medium text-gray-700 text-center leading-tight">
                                                    <p className="font-medium">
                                                        {t('exam.title')}
                                                    </p>
                                                </span>
                                            </div>
                                        </BetaPopup>
                                    ) : null)}
                            </div>
                        </div>
                    )}

                    {/* Show selected tab content using table lookup */}
                    {activeTab !== '' && (
                        <div className="flex flex-col items-center w-full h-full">
                            <div
                                className={`h-full px-10 pt-11 bg-clip-padding backdrop-blur-md backdrop-opacity-90 bg-white/60 saturate-100 backdrop-contrast-100 w-[350px] sm:w-[500px] md:w-[600px] lg:w-[845px] ${
                                    activeTab === 'FILE'
                                        ? 'pb-8 xl:pb-4 2xl:py-8'
                                        : activeTab === 'EXAM'
                                          ? 'pb-4 2xl:py-8'
                                          : 'pb-4'
                                }`}
                            >
                                {
                                    tabContents[
                                        activeTab as keyof typeof tabContents
                                    ]
                                }
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    )
}

export default ExoGenerator
