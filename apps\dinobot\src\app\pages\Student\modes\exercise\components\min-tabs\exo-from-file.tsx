import React, { useEffect } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Upload } from 'lucide-react'
import { toast } from 'sonner'
import { selectUseExoModeStore } from '@dinobot/stores'
import { CustomFile } from '@dinobot/stores/lib/exercise-mode-store/exercise-store'
import { useTranslation } from 'react-i18next'
import { Button, InfoTooltip, Slider } from '@dinobot/components-ui'

interface Props {
    onGenerate: () => Promise<void>
}

function ExoFromFileMin({ onGenerate }: Props) {
    const data = selectUseExoModeStore.use.exoInfoFromFile()
    const setData = selectUseExoModeStore.use.updateExoInfoFromFile()
    const setMode = selectUseExoModeStore.use.setMode()

    function convertToBase64(file: File): Promise<CustomFile> {
        return new Promise((resolve, reject) => {
            const fileReader = new FileReader()

            fileReader.onload = function (fileLoadedEvent) {
                const base64 = fileLoadedEvent.target?.result
                const result: CustomFile = {
                    extension: file.name.split('.').pop() || '',
                    name: file.name,
                    data: base64?.toString().split(',')[1] ?? ''
                }

                resolve(result)
            }

            fileReader.onerror = function (error) {
                reject(error)
            }

            fileReader.readAsDataURL(file)
        })
    }

    const handleFormChange = async (
        field: keyof typeof data,
        value: File | number | string
    ) => {
        let new_value: CustomFile

        if (value instanceof File) {
            new_value = await convertToBase64(value)
            console.log(new_value)
            setData(field, new_value)
            return
        }
        setData(field, value)
    }
    const {t} = useTranslation(['app/mode'],{keyPrefix:'exo.mini-tabs.file'})
    useEffect(() => {
        setMode('FROM_FILE')
    }, [])

    const handleSubmit = async () => {
        if (data.exo) {
            setMode('FROM_FILE')
            // router.push("/train")
            await onGenerate()
        } else {
            toast.error(t('error'))
        }
    }

    return (
        <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-1">
                <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                    {t('generate')} <InfoTooltip message={t('chois-chap')} />
                </div>
                <div>
                    <p className="text-[10px] text-dinoBotGray">
                        {t('download')}
                    </p>
                    <p className="text-[10px] font-light text-dinoBotGray">
                        {t('rappelle')}
                    </p>
                </div>
                <div className="w-fit flex flex-col gap-2   py-3">
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-5">
                        <div>
                            <label
                                htmlFor="exo-file"
                                className="text-[10px] text-dinoBotGray"
                            >
                                {t('enonce')}
                            </label>
                            <label
                                htmlFor="exo-file"
                                className={`flex items-center px-2 gap-1 w-40 h-6 text-[10px]  text-dinoBotDarkGray rounded-md cursor-pointer hover:bg-dinoBotLightBlue transition-colors duration-200 ${data.exo ? 'bg-dinoBotGreen/20' : 'bg-dinoBotLightGray'}`}
                            >
                                {data.exo ? (
                                    <CircleCheckBig className="size-3" />
                                ) : (
                                    <Upload className="size-3" />
                                )}
                                <p>
                                    {data.exo ? t('remp-file') : t('down-file')}{' '}
                                    <span className="text-dinoBotRed">*</span>
                                </p>
                            </label>
                            <input
                                className="hidden"
                                type="file"
                                name="exo-file"
                                id="exo-file"
                                accept="application/pdf"
                                onChange={e => {
                                    if (e.target.files)
                                        handleFormChange(
                                            'exo',
                                            e.target.files[0]
                                        )
                                }}
                            />
                        </div>
                        <div>
                            <label
                                htmlFor="solution-file"
                                className="text-[10px] text-dinoBotGray"
                            >
                                {t('correction')}
                            </label>
                            <label
                                htmlFor="solution-file"
                                className={`flex items-center px-2 gap-1 w-40 h-6 text-[10px]  text-dinoBotDarkGray rounded-md cursor-pointer hover:bg-dinoBotLightBlue transition-colors duration-200 ${data.solution ? 'bg-dinoBotGreen/20' : 'bg-dinoBotLightGray'}`}
                            >
                                {data.solution ? (
                                    <CircleCheckBig className="size-3" />
                                ) : (
                                    <Upload className="size-3" />
                                )}
                                <p>
                                    {data.solution
                                        ? 'Remplacez le fichier'
                                        : 'Téléchargez un fichier'}{' '}
                                </p>
                            </label>
                            <input
                                className="hidden"
                                type="file"
                                name="exo-file"
                                id="solution-file"
                                accept="application/pdf"
                                onChange={e => {
                                    if (e.target.files)
                                        handleFormChange(
                                            'solution',
                                            e.target.files[0]
                                        )
                                }}
                            />
                        </div>
                    </div>
                    <p className="text-dinoBotGray text-[8px]">
                        <span className="text-dinoBotRed">*</span> {t('max')}
                    </p>
                </div>
            </div>

            <div className="w-full flex flex-col sm:flex-row justify-between gap-3">
                <div className="w-full sm:w-3/5 md:w-2/5 ">
                    <div className="flex gap-1 items-center justify-between text-sm font-bold text-dinoBotDarkGray">
                        {t('difficulty')} <span>{data.difficulty}/3</span>
                    </div>
                    <div className="py-3">
                        <Slider
                            defaultValue={[data.difficulty]}
                            min={1}
                            max={3}
                            step={1}
                            className={'w-full '}
                            onValueChange={value =>
                                handleFormChange('difficulty', value[0])
                            }
                        />
                    </div>
                </div>
            </div>

            <div className="w-full flex justify-center items-center mt-8">
                <Button
                    className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
                    onClick={handleSubmit}
                >
                    {t('submit')}
                </Button>
            </div>
        </div>
    )
}

export default ExoFromFileMin
