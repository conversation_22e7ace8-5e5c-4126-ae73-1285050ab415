import React, { useEffect, useState } from 'react'
import { toast } from 'sonner'
import { getLangProps } from '@dinobot/utils'
import { Domain } from '@prisma/client'
import {useAccountStore} from '@dinobot/stores'
import { useExerciseData } from '../../../hooks'
import Cookies  from 'js-cookie'
import { Chapter, Part, Domain as DomainType } from '../../../trainig.types'
import useExoModeStore, { ExoInfoFromDb, selectUseExoModeStore } from '@dinobot/stores/lib/exercise-mode-store/exercise-store'
import { useTranslation } from 'react-i18next'
import { useLocation, useNavigate } from 'react-router-dom'
import { Button, Input, MultiSelect, Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue, Slider, Textarea } from '@dinobot/components-ui'

interface ExoFromDbProps {
    domains: Domain[]
    // getChapters: (domain: string, level: string) => Promise<Chapter[]>
    // getParts: (chapterId: string) => Promise<Part[]>
}

function ExoFromDb({ domains }: ExoFromDbProps) {
    const formData = selectUseExoModeStore.use.exoInfoFromDb()
    const [selectedChapter, setSelectedChapter] = useState<string | undefined>(
        formData.chapterId
    )
    const path = useLocation().pathname
    const [parts, setParts] = useState<Part[]>([])
    const [chapters, setChapters] = useState<Chapter[]>([])
    const handleFormChange = selectUseExoModeStore.use.updateExoInfoFromDb()
    const [showError, setShowError] = useState<boolean>(false)
    const setMode = useExoModeStore(state => state.setMode)
    const { t ,i18n } = useTranslation(['app/mode'], { keyPrefix: 'exo.tab.db' })
    const { t: tMultiSelect } = useTranslation(['components/multiselect'])
    const navigate = useNavigate()
    const lang = i18n.language
    const { user } = useAccountStore()
    const session = JSON.parse(Cookies.get('session') || '{}')
    const userLevelId = session?.user?.userLevel?.id
    
    // Use hooks for API calls
    const { useChaptersByDomainAndLevel, usePartsByChapter } = useExerciseData()
    const topicId = Cookies.get('topicId')
    
    // Get chapters when domain and level are available
    const { data: chaptersData } = useChaptersByDomainAndLevel(
        topicId ? parseInt(topicId) : undefined,
        userLevelId
    )
    
    // Get parts when chapter is selected
    const { data: partsData } = usePartsByChapter(selectedChapter)

    useEffect(() => {
        setMode('FROM_DB')
    }, [])

    // Update chapters when data is available
    useEffect(() => {
        if (chaptersData) {
            setChapters(chaptersData as Chapter[])
        }
    }, [chaptersData])
    
    // Update parts when data is available
    useEffect(() => {
        if (partsData) {
            setParts(partsData as Part[])
        }
    }, [partsData])

    useEffect(() => {
        if (selectedChapter) {
            handleFormChange('chapterId', selectedChapter)
        }
    }, [selectedChapter])

    const normalizeExerciseNumber = (value: number): number => {
        if (value > 10) return 10
        if (value < 1) return 1
        return value
    }

    const showLimitInfo = (value: number) => {
        if (value > 10) {
            toast.info(t('tinfo.n10'), { duration: 2500 })
        } else if (value < 1) {
            toast.info(t('tinfo.n1'), { duration: 2500 })
        }
    }

    const handleNumberChange = (
        field: keyof ExoInfoFromDb,
        value: number,
        defaultValue: number
    ) => {
        try {
            const normalizedValue = normalizeExerciseNumber(value)
            showLimitInfo(value)
            handleFormChange(field, normalizedValue)
        } catch (error) {
            handleFormChange(field, defaultValue)
        }
    }

    const handleQuestionNumberChange = (value: number) => {
        handleNumberChange('qstNbr', value, 5)
    }

    const handleExerciseNumberChange = (value: number) => {
        handleNumberChange('exoNbr', value, 3)
    }

    useEffect(() => {
        if (formData.qstNbr) handleQuestionNumberChange(formData.qstNbr)
    }, [formData.qstNbr])

    useEffect(() => {
        if (formData.exoNbr) handleExerciseNumberChange(formData.exoNbr)
    }, [formData.exoNbr])

    const submit = () => {
        if (formData.chapterId && formData.partIds.length > 0) {
            if (formData.qstNbr && formData.exoNbr) {
                setShowError(false)
                setMode('FROM_DB')
                navigate(`${path}/train`)
            } else {
                toast.error(t('tinfo.error'))
            }
        } else {
            setShowError(true)
            toast.info(t('tinfo.info'))
        }
    }

    return (
        <div className="flex flex-col gap-4 w-full">
            <div className="flex flex-col gap-y-9">
                <div className="flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('subject.name')}{' '}
                    </div>
                    <Select
                        value={formData.domainName}
                        onValueChange={value => {
                            handleFormChange('domainName', value)
                            handleFormChange('chapterId', '') // Reset chapterId when domain changes
                            handleFormChange('partIds', []) // Reset partIds when domain changes
                            setSelectedChapter(undefined) // Reset selectedChapter state
                            const selectedDomain = domains.find(
                                domain => domain.name === value
                            )
                            if (selectedDomain) {
                                Cookies.set(
                                    'topicId',
                                    selectedDomain.id.toString()
                                )
                                Cookies.set('topic', selectedDomain.name)
                            }
                        }}
                    >
                        <SelectTrigger className="max-w-full">
                            <SelectValue
                                placeholder={t('subject.placeholder')}
                            />
                        </SelectTrigger>
                        <SelectContent
                            className={`${domains.length > 5 ? 'h-48' : 'h-fit'}`}
                        >
                            <SelectGroup>
                                {domains.map(domain => (
                                    <SelectItem
                                        key={domain.id}
                                        value={domain.name}
                                    >
                                        {getLangProps({
                                            obj: domain,
                                            base: 'name',
                                            lang
                                        })}
                                    </SelectItem>
                                ))}
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('chois.title')}{' '}
                    </div>
                    <Select
                        value={formData.chapterId}
                        onValueChange={value => {
                            handleFormChange('partIds', [])
                            setSelectedChapter(value)
                        }}
                        disabled={chapters.length <= 0 || !formData.domainName}
                    >
                        <SelectTrigger className="max-w-full">
                            <SelectValue placeholder={t('chois.placeholder')} />
                        </SelectTrigger>
                        <SelectContent
                            className={`${chapters.length > 5 ? 'h-48' : 'h-fit'}`}
                        >
                            <SelectGroup>
                                {chapters.map(chapter => (
                                    <SelectItem
                                        key={chapter.id}
                                        value={chapter.id}
                                    >
                                        {getLangProps({
                                            obj: chapter,
                                            base: 'title',
                                            lang
                                        })}
                                    </SelectItem>
                                ))}
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('part.title')}{' '}
                    </div>
                    <MultiSelect
                        options={parts.map(part => ({
                            label: getLangProps({
                                obj: part,
                                base: 'name',
                                lang
                            }),
                            value: part.id
                        }))}
                        onValueChange={values =>
                            handleFormChange('partIds', values)
                        }
                        value={formData.partIds}
                        disabled={parts.length <= 0 || !formData.chapterId}
                        placeholder={t('part.placeholder')}
                        badgeclassName="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/80 text-white"
                        translations={{
                            selectAll: tMultiSelect('selectAll'),
                            search: tMultiSelect('search'),
                            noResults: tMultiSelect('noResults'),
                            clear: tMultiSelect('clear'),
                            close: tMultiSelect('close'),
                            more: tMultiSelect('more')
                        }}
                    />
                </div>

                <div className="w-full flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('exonumber')}
                    </div>
                    <Input
                        type="number"
                        value={formData.exoNbr}
                        min={1}
                        max={10}
                        className="h-9 rounded-md"
                        onChange={e =>
                            handleFormChange(
                                'exoNbr',
                                parseInt(e.target.value) || 1
                            )
                        }
                    />
                </div>
                <div className="w-full flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('questions-number')}
                    </div>
                    <Input
                        type="number"
                        value={formData.qstNbr}
                        min={1}
                        max={10}
                        className="h-9 rounded-md"
                        onChange={e =>
                            handleFormChange(
                                'qstNbr',
                                parseInt(e.target.value) || 1
                            )
                        }
                    />
                </div>

                <div className="w-full sm:w-4/5 md:w-3/5">
                    <div className="flex gap-5 items-center text-sm font-bold text-dinoBotDarkGray">
                        <span>{t('difficulty')}</span>
                        <div className="flex-1">
                            <Slider
                                defaultValue={[formData.difficulty]}
                                min={0}
                                max={3}
                                step={1}
                                className="w-full"
                                onValueChange={value =>
                                    handleFormChange('difficulty', value[0])
                                }
                            />
                        </div>
                        <span>{formData.difficulty}/3</span>
                    </div>
                </div>

                <div className="flex flex-col gap-1">
                    <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {' '}
                        {t('custom-prompt')}{' '}
                        <span className="font-normal">{t('opt')}</span>{' '}
                    </div>
                    <div className="mt-2">
                        <Textarea
                            placeholder={t('redigez')}
                            value={formData.customPrompt}
                            onChange={e => {
                                if (e.target.value.length <= 1000)
                                    handleFormChange(
                                        'customPrompt',
                                        e.target.value
                                    )
                            }}
                        ></Textarea>
                        <p className="text-xs text-right mt-1 text-dinoBotGray">
                            {formData.customPrompt.length ?? 0}/1000{' '}
                            {t('caracteres')}
                        </p>
                    </div>
                </div>
            </div>
            <div className="w-full flex justify-center items-center mt-2">
                <Button
                    className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
                    onClick={submit}
                >
                    {t('submit')}
                </Button>
            </div>
        </div>
    )
}

export default ExoFromDb
