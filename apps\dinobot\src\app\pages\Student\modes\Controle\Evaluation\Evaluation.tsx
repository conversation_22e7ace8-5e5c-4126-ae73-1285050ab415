import React from 'react'
import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import Timer from './components/Timer/timer'
import ProgrammedEvaluationPreview from './components/programmed-evaluation-preview'
import ProgrammedAnswerControl from './components/programmed-answer-evaluation'
import MediaFullView from './components/assets-viewer/media-full-view'
import { PlannedEvaluationWithPartialRelations } from '@dinobot/prisma'
import {
    ControlPartialWithRelations,
    ControlWithPartialRelations,
    ControlWithPartialRelationsSchema
} from '@dinobot/prisma'
import { z } from 'zod'
import { usePlannedEvaluation, useSignedControlMedia } from '../hooks/useEvaluation'

const CONTROL_TIME: number = Number.parseInt(import.meta.env.APP_CONTROL_TIME || '3600')

function Evaluation() {
    const { evaluationId } = useParams<{ evaluationId: string }>()
    const { i18n } = useTranslation()
    const dir = getLangDir(i18n.language)
    const apiClient = useAuthApiClient()
    const { data: plannedEvaluation, isLoading, error } = usePlannedEvaluation(evaluationId)

    

    const time = new Date()

    const mediaExpiresIn = plannedEvaluation?.timeLimit ?? 3600
    const { getSignedUrl } = useSignedControlMedia()

    const { data: transformedEvaluation } = useQuery({
        queryKey: ['transformed-evaluation', evaluationId, plannedEvaluation?.control?.id],
        queryFn: async () => {
            if (!plannedEvaluation?.control) return plannedEvaluation

            const transformControleData = async (
                data: z.infer<typeof ControlWithPartialRelationsSchema>
            ): Promise<ControlWithPartialRelations | null> => {
                if (!data) return null

                const exercises = await Promise.all(
                    data?.exercises?.map(async (item, i) => {
                        const questions = await Promise.all(
                            item?.questions?.map(async (q, i) => {
                                const medias = q?.medias
                                    ? await Promise.all(
                                          q.medias.map(async m => {
                                              const url = m.fileUrl
                                                  ? await getSignedUrl(m.fileUrl, mediaExpiresIn)
                                                  : undefined
                                              return {
                                                  ...m,
                                                  signedUrl: url
                                              }
                                          })
                                      )
                                    : undefined

                                return {
                                    id: q.id,
                                    content: q.content,
                                    answer: undefined,
                                    feedback: q.solution,
                                    desmosCode: q?.desmosCode,
                                    type: q?.type,
                                    medias
                                }
                            }) || []
                        )
                        const medias =
                            item && item.medias
                                ? await Promise.all(
                                      item.medias.map(async m => {
                                          const url = m.fileUrl
                                              ? await getSignedUrl(m.fileUrl, mediaExpiresIn)
                                              : undefined
                                          return {
                                              ...m,
                                              signedUrl: url
                                          }
                                      })
                                  )
                                : []

                        return {
                            ...item,
                            id: item.id,
                            title: 'exercice: ' + (i + 1),
                            score: 0,
                            questions,
                            medias
                        }
                    }) || []
                )

                return {
                    ...data,
                    exercises: exercises as any
                }
            }

            const controle = await transformControleData(
                plannedEvaluation.control! as ControlWithPartialRelations
            )

            return {
                ...plannedEvaluation,
                control: controle as any
            } as PlannedEvaluationWithPartialRelations
        },
        enabled: !!plannedEvaluation
    })

    const finalEvaluation = transformedEvaluation || plannedEvaluation

    time.setSeconds(time.getSeconds() + (finalEvaluation?.timeLimit ?? 0))
    if (isLoading) return <div>Loading...</div>
    if (error) return <div>Error loading evaluation</div>
    if (!plannedEvaluation) return <div>Evaluation not found</div>
    return (
        <div dir={dir} className="flex size-full flex-col xl:flex-row relative">
            <div className="xl:absolute fixed xl:top-5 xl:bottom-auto bottom-2 left-1/2 -translate-x-1/2  z-20">
                <Timer timer={time} timeLimit={finalEvaluation?.timeLimit||0} />
            </div>
            <div className=" xl:w-[50vw] w-full border-l-gray-700 border-2 relative">
                <ProgrammedEvaluationPreview
                    control={
                        finalEvaluation?.control as ControlWithPartialRelations
                    }
                    availableDate={finalEvaluation?.availableDate}
                    plannedEvaluation={finalEvaluation as PlannedEvaluationWithPartialRelations}
                />
            </div>
            <div className=" xl:w-[50vw] w-full border-r-gray-700 border-2">
                <ProgrammedAnswerControl
                    plannedEvaluation={finalEvaluation as PlannedEvaluationWithPartialRelations}
                />
            </div>
            <MediaFullView />
        </div>
    )
}

export default Evaluation
