import { Button ,type messageFileType, PDFReader } from '@dinobot/components-ui'
import { useResizeObserver ,useScrollAnchor } from '@dinobot/hooks'
import {usePDFViewerStore,selectUsePDFViewerStore, useSizeSaveStore } from '@dinobot/stores'
import { cn,type Session } from '@dinobot/utils'
import { useChat } from '@ai-sdk/react'
import { useEffect, useReducer } from 'react'
import { useExamsStore } from '@dinobot/stores'
import { getLangDir } from 'rtl-detect'
import { ChatCortexDialog,EmptyScreen,ChatList } from '@dinobot/components-ui'
import { ChatPanel } from './ChatPanel'
import { useLocation } from 'react-router-dom'
import { useChat as useChatHook } from '../hooks/useChat'
import { useChatStore } from '@dinobot/stores'
import CheckMessageChat from './CheckMessageChat'
import { useAppContext } from '../../../../contexts/AppContext'
import { useTranslation } from 'react-i18next'

type ChatProps = {
    session?: Session
    idp?: string
}
type chatStateType = {
    imageFile: string
    fileExtension: string
    previousPath: string
    messagesFileMap: Map<number, messageFileType>
}

const Chat = ({ session, idp }: ChatProps) => {
    const {t,i18n} = useTranslation(['app/chat'],{keyPrefix:"[id]"})
    const { exercise, showSolution, setShowSolution } = useExamsStore()
    const { refreshChatList, setRefreshChatList } = useChatStore()
    const {config} = useAppContext()
    const pathname = useLocation().pathname
    const { checkChatExistence, getFile } = useChatHook()

    useEffect(() => {
        if (pathname.endsWith('/exam')) {
            document.cookie = 'feature=Exam; path=/'
        } else {
            document.cookie = 'feature=Chat; path=/'
        }
    }, [pathname])
    const initialState: chatStateType = {
        imageFile: '',
        fileExtension: '',
        previousPath: '',
        messagesFileMap: new Map<number, messageFileType>()
    }

    // Use reducer with simplified syntax for partial state updates
    const [
        { imageFile, fileExtension, previousPath, messagesFileMap },
        dispatch
    ] = useReducer(
        (state: chatStateType, change: Partial<chatStateType>) => ({
            ...state,
            ...change
        }),
        initialState
    )

    const { isPDFViewerOpen, setIsPDFViewerOpen } = usePDFViewerStore()
    const currentPDF = selectUsePDFViewerStore.use.currentPDF()

    const locale = i18n.language
    const dir = getLangDir(locale)

    const {
        messages,
        input,
        handleInputChange,
        handleSubmit,
        id,
        setMessages,
        status,
        append,
        error
    } = useChat({
        id: idp,
        api: `${config.apiUrl}/api/chat${locale && `?locale=${locale}`}`,
        body: {
            imageFile,
            fileExtension,
            currentPDF: exercise
        },
        credentials: 'include',
        // fetch:(url, options) => apiClient.post(url.toString(), options),
    })
    
    const {
        messagesRef,
        scrollRef,
        visibilityRef,
        isAtBottom,
        scrollToBottom
    } = useScrollAnchor()

    const [size] = useResizeObserver<HTMLDivElement>(scrollRef)
    const { setSize } = useSizeSaveStore()

    // Load initial messages if idp exists
    useEffect(() => {
        const loadInitialMessages = async () => {
            if (idp) {
                const existingChat = await checkChatExistence(`/chat/${idp}`)
                if (existingChat?.messages) {
                    // Créer une nouvelle Map pour stocker les fichiers
                    const newMap = new Map<number, messageFileType>(
                        messagesFileMap
                    )

                    // Charger les messages
                    setMessages(
                        existingChat.messages.map((msg, index) => {
                            if (msg.file?.id) {
                                getFile(msg.file.id).then(filedata => {
                                    if (
                                        filedata &&
                                        filedata.userId &&
                                        filedata.name &&
                                        filedata.file
                                    ) {
                                        // Ajouter le fichier à la Map avec l'ID du message comme clé
                                        newMap.set(index, {
                                            ...filedata,
                                            messageId: msg.id
                                        })
                                        dispatch({
                                            messagesFileMap: new Map(newMap)
                                        })
                                    }
                                })
                            }
                            return {
                                id: msg.id,
                                role: msg.role as
                                    | 'user'
                                    | 'assistant'
                                    | 'system'
                                    | 'data',
                                content: msg.content,
                                name: undefined
                            }
                        })
                    )
                }
            }
        }
        loadInitialMessages()
    }, [idp, setMessages, checkChatExistence, getFile, messagesFileMap, dispatch])
    useEffect(() => {
        if (refreshChatList) {
            window.location.reload()
            setRefreshChatList(false)
        }
    }, [refreshChatList, setRefreshChatList])

    useEffect(() => {
        setSize(size)
    }, [setSize, size])

    // Handle keyboard shortcuts
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            // Escape key to close PDF viewer
            if (e.key === 'Escape' && isPDFViewerOpen) {
                setIsPDFViewerOpen(false)
            }

            // Ctrl+Enter to submit message
            if (e.ctrlKey && e.key === 'Enter' && input.trim()) {
                handleSubmit()
            }
        }

        window.addEventListener('keydown', handleKeyDown)
        return () => window.removeEventListener('keydown', handleKeyDown)
    }, [handleSubmit, input, isPDFViewerOpen, setIsPDFViewerOpen])

    // Créez une fonction pour gérer l'envoi de messages avec fichiers
    const handleSubmitWithFile = (e?: { preventDefault?: () => void }) => {
        e?.preventDefault?.()

        // Si un fichier est présent, assurez-vous qu'il est inclus dans la requête
        if (imageFile) {
            // useChat va automatiquement utiliser le body configuré ci-dessus
            handleSubmit(e)

            // Réinitialiser le fichier après l'envoi
            dispatch({ imageFile: '', fileExtension: '' })
        } else {
            // Envoi normal sans fichier
            handleSubmit(e)
        }
    }

    // Fonction pour ajouter un fichier à la Map
    const addFileData = (data: messageFileType) => {
        const newMap = new Map(messagesFileMap)
        newMap.set(messages.length, data)
        dispatch({ messagesFileMap: newMap })
    }
    return (
        <div className="group w-full overflow-auto pl-0 " ref={scrollRef}>
            <CheckMessageChat
                messages={messages}
                setMessages={setMessages}
                session={session}
                id={id}
                previousPath={previousPath}
                setPreviousPath={path => dispatch({ previousPath: path })}
                status={status}
            />
            {isPDFViewerOpen ? (
                <div className="fixed top-0 left-0 w-screen flex bg-black/80 justify-center items-center z-[100000]">
                    <PDFReader file={currentPDF} />
                    <Button
                        variant="ghost"
                        className="absolute top-4 right-4 text-white hover:bg-black/20"
                        onClick={() => setIsPDFViewerOpen(false)}
                    >
                        ✕
                    </Button>
                </div>
            ) : null}

            <div
                className={cn('pb-[200px] mt-4 pt-4 md:pt-2 ')}
                ref={messagesRef}
            >
                {exercise ? 
                        showSolution && exercise?.solutionMedia ? (
                            <div className=" w-[600px] 2xl:ml-20 fixed hidden xl:block">
                                <div className="absolute top-4 right-3 text-lg  ">
                                    <Button
                                        variant={'link'}
                                        className="text-dinoBotVibrantBlue hover:text-dinoBotVibrantBlue"
                                        onClick={() => setShowSolution(false)}
                                    >
                                        {t('voire')}
                                    </Button>
                                </div>
                                <PDFReader
                                    file={{
                                        name: `${exercise?.title} - solution`,
                                        type: 'pdf',
                                        data: `data:application/pdf;base64,${exercise?.solutionMedia}`
                                    }}
                                    sideViewer={true}
                                />
                            </div>
                        ) : (
                            <div className="w-[600px] 2xl:ml-20 fixed hidden xl:block">
                                <div className="absolute top-4 right-3 text-lg">
                                    <Button
                                        variant={'link'}
                                        className="text-dinoBotCyan hover:text-dinoBotCyan"
                                        onClick={() => setShowSolution(true)}
                                    >
                                        {t('voirc')}
                                    </Button>
                                </div>
                                <PDFReader
                                    file={{
                                        name: `${exercise?.title} - énoncé`,
                                        type: 'pdf',
                                        data: `data:application/pdf;base64,${exercise?.assignmentMedia}`
                                    }}
                                    sideViewer={true}
                                />
                            </div>
                        )
                : null}
                <div
                    className={`${exercise ? (dir === 'ltr' ? 'xl:ml-[50vw] 2xl:ml-[35vw]' : 'xl:mr-[46vw] 2xl:mr-[35vw]') : ''} transition-all duration-300`}
                >
                    {messages.length || exercise ? (
                        <ChatList
                            messages={messages}
                            exercise={exercise}
                            status={status}
                            error={error}
                            messagesFileMap={messagesFileMap}
                        />
                    ) : (
                        session && <EmptyScreen session={session} />
                    )}
                </div>

                <div className="h-px w-full" ref={visibilityRef} />
            </div>
            <ChatPanel
                id={id}
                input={input}
                status={status}
                setInput={handleInputChange}
                imageFile={imageFile}
                setImageFile={value => dispatch({ imageFile: value })}
                fileExtension={fileExtension}
                setFileExtension={value => dispatch({ fileExtension: value })}
                addFileData={addFileData}
                isAtBottom={isAtBottom}
                scrollToBottom={scrollToBottom}
                session={session}
                handleSubmit={handleSubmitWithFile}
                append={append}
                messages={messages}
            />
            <ChatCortexDialog />
        </div>
    )
}

export default Chat
