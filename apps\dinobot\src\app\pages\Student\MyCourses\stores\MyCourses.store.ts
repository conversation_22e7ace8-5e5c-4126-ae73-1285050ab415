import { create } from 'zustand'
import { MyCoursesStore } from '../my-courses.types'

export const useMyCoursesStore = create<MyCoursesStore>((set) => ({
  // State
  courses: [],
  isLoading: false,
  error: null,
  dialogOpen: false,

  // Actions
  setCourses: (courses) => set({ courses }),

  addCourse: (course) =>
    set((state) => ({
      courses: [...state.courses, course],
      dialogOpen: false
    })),

  setLoading: (isLoading) => set({ isLoading }),

  setError: (error) => set({ error }),

  setDialogOpen: (dialogOpen) => set({ dialogOpen }),

  reset: () => set({
    courses: [],
    isLoading: false,
    error: null,
    dialogOpen: false,
  }),
}))
