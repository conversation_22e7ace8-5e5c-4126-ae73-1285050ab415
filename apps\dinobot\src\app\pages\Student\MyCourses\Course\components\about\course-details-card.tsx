import { useQuery } from '@tanstack/react-query'
import { ClassWithPartialRelations } from '@dinobot/prisma'
import { useTranslation } from 'react-i18next'
import React from 'react'
import { useAuthApiClient } from '../../../../../contexts/AppContext'

type CourseDetailsCardProps = {
    id?: string
}

function CourseDetailsCard({ id }: CourseDetailsCardProps) {
    const apiClient = useAuthApiClient()
    const { t } = useTranslation(['app/courses/index'])

    const { data: course, isLoading, error } = useQuery({
        queryKey: ['course-details', id],
        queryFn: async (): Promise<ClassWithPartialRelations | null> => {
            if (!id) return null
            const response = await apiClient.get(`/api/classes/student/${id}`)
            return response.data
        },
        enabled: !!id
    })

    if (isLoading) {
        return <div className="h-32 w-full bg-gray-200 animate-pulse rounded-sm m-2"></div>
    }

    if (error || !course) {
        return <div className="h-32 w-full bg-red-100 rounded-sm m-2 flex items-center justify-center">
            Error loading course details
        </div>
    }

    return (
        <div
            className="h-fit w-full p-6 m-2 rounded-sm font-medium text-dinoBotWhite text-xl flex gap-2 items-center "
            style={{ backgroundColor: course?.classColor as string }}
        >
            <div className="flex flex-col">
                <h1 className="font-bold text-xl">{course?.name}</h1>
                <h2 className="font-light text-sm">
                    {t('card.title')}
                </h2>
                <span>
                    {course?.mainTeacher?.lastName}{' '}
                    {course?.mainTeacher?.firstName}
                </span>
            </div>
        </div>
    )
}

export default CourseDetailsCard
