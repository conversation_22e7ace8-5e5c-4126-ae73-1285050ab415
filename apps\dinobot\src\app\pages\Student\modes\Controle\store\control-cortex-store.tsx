// stores/newChatStore.ts
import { create } from 'zustand'

type CortexStore = {
    open: boolean
    idanswer: number
    idcontent: number
    insert: boolean
    cortexValue: string[][]
    text: string[][]
    value: string[][]
    setid: (idanswer: number, idcontent: number) => void
    openCortexDialog: () => void
    closeCortexDialog: () => void
    setCortexValue: (value: string, answerid: number, contentid: number) => void
    resetCortexValue: () => void
    insertNow: () => void
    setText: (value: string, answerid: number, contentid: number) => void
    initValue: (value: string[][]) => void
    setValue: (value: string, answerid: number, contentid: number) => void
    reset: () => void
}

const useCortexStore = create<CortexStore>((set, get) => ({
    open: false,
    idanswer: 0,
    idcontent: 0,
    cortexValue: [['']],
    insert: false,
    text: [['']],
    value: [['']],

    closeCortexDialog() {
        set({ open: false })
    },

    openCortexDialog() {
        set({ open: true })
    },

    setid(idanswer, idcontent) {
        set({ idanswer: idanswer, idcontent: idcontent })
    },

    setCortexValue(value, answerid, contentid) {
        set(state => {
            const newCortexValue = [...state.cortexValue]
            if (!newCortexValue[answerid]) {
                newCortexValue[answerid] = []
            }
            newCortexValue[answerid][contentid] = value
            return { ...state, cortexValue: newCortexValue }
        })
    },

    resetCortexValue() {
        set({ cortexValue: [] })
    },

    insertNow() {
        set({ insert: true })
        setTimeout(
            () =>
                set(val => {
                    const newState = { ...val }
                    if (newState.cortexValue.length > 0)
                        newState.cortexValue[get().idanswer][get().idcontent] =
                            ''
                    newState.insert = false
                    newState.open = false
                    return newState
                }),
            100
        )
    },

    setText(value, answerid, contentid) {
        set(state => {
            const newText = [...state.text]
            if (!Array.isArray(newText[answerid])) {
                newText[answerid] = []
            }
            newText[answerid][contentid] = value
            return { ...state, text: newText }
        })
    },

    initValue(value) {
        set({ value: value, cortexValue: value, text: value })
    },

    setValue(value, answerid, contentid) {
        set(val => {
            const newState = { ...val }
            if (newState.value.length > 0)
                newState.value[answerid][contentid!]! = value
            return newState
        })
    },
    reset() {
        set({ value: [], text: [], cortexValue: [] })
    }
}))

export default useCortexStore
