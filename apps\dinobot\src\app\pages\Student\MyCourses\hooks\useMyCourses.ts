import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { JoinClassData } from '../my-courses.types'
import { useMyCoursesStore } from '../stores/MyCourses.store'
import { FeatureFlagName } from '@prisma/client'
import { useAuthApiClient } from '../../../contexts/AppContext'
import { ClassWithPartialRelations } from '@dinobot/prisma'

export const useMyCourses = () => {
  const apiClient = useAuthApiClient()
  const queryClient = useQueryClient()

  const {
    setCourses,
    addCourse,
    setLoading,
    setError,
    setDialogOpen,
    courses,
    isLoading: storeLoading,
    error,
    dialogOpen
  } = useMyCoursesStore()

  // Fetch student classes
  const {
    data: studentClasses,
    isLoading: queryLoading,
    error: queryError
  } = useQuery({
    queryKey: ['student-classes'],
    queryFn: async (): Promise<ClassWithPartialRelations[]> => {
      const response = await apiClient.get('/api/classes/student/classes')
      return response.data
    }
  })

  // Handle side effects with useEffect (like the original)
  useEffect(() => {
    if (studentClasses) {
      setCourses(studentClasses)
    }
  }, [studentClasses, setCourses])

  useEffect(() => {
    if (queryError) {
      setError(queryError.message)
    }
  }, [queryError, setError])


  // Join class by code mutation
  const joinClassMutation = useMutation({
    mutationFn: async (data: JoinClassData): Promise<ClassWithPartialRelations> => {
      const response = await apiClient.post(`/api/classes/join/${data.code}`)
      return response.data
    }
  })

  const isLoading = storeLoading || queryLoading

  return {
    // Data
    courses: courses.length > 0 ? courses : studentClasses || [],

    // Loading states
    isLoading,
    isJoining: joinClassMutation.isPending,

    // Error states
    error: error || queryError?.message,

    // Dialog state
    dialogOpen,
    setDialogOpen,

    // Actions
    joinClass: joinClassMutation.mutate,
    joinClassAsync: joinClassMutation.mutateAsync,

    // Store actions
    setCourses,
    setError,
    reset: useMyCoursesStore.getState().reset
  }
}

export default useMyCourses
