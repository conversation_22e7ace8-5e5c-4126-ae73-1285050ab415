import { Button } from '@dinobot/components-ui'
import { File<PERSON>enLine, Trash2 } from 'lucide-react'
import React from 'react'
import DialogExoQuestionsFromDB from './dialogs/dialog-exo-questions-from-db'
import DialogExoQuestionsFromFile from './dialogs/dialog-exo-questions-from-file'
import DialogExoQuestionsFromExams from './dialogs/dialog-exo-questions-from-exams'
import { useEvaluationParamsStore } from '../../store/evaluation-params.store'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import { cn } from '@dinobot/utils'

type QuestionContentProps = {
    quesNum: number
    exoIndex: number
    canRemove: boolean
}

const QuestionContent = ({
    exoIndex,
    canRemove,
    quesNum
}: QuestionContentProps) => {
    const { t, i18n } = useTranslation('teacher/myClass/evaluation/next/exo/select')
    const dir = getLangDir(i18n.language)
    const { removeQuestion, addQuestions, evalProps } =
        useEvaluationParamsStore()
    const levelName = evalProps?.assignedClass?.level?.name
    return (
        <div className="mt-2 relative">
            <Button
                variant="outline"
                className={`text-dinoBotRed hover:text-dinoBotRed/70 absolute top-9 right-2 px-1 border-dinoBotRed bg-transparent ${canRemove ? '' : 'hidden'} `}
                onClick={() => removeQuestion(exoIndex, quesNum)}
            >
                <Trash2 />
            </Button>
            <h4
                className={cn(
                    'text-dinoBotBlue mt-1',
                    dir === 'rtl' && 'text-end'
                )}
            >
                {t('title')} {quesNum + 1}
            </h4>
            <div className="flex items-center justify-evenly rounded-lg bg-dinoBotLightGray/50 border-dinoBotLightGray border p-4 px-32">
                <DialogExoQuestionsFromDB
                    exoIndex={exoIndex}
                    quesNum={quesNum}
                />
                {levelName === 'Terminale' /*||levelName==='Première'*/ ||
                levelName === 'Troisième' ? (
                    <DialogExoQuestionsFromExams
                        title={
                            levelName === 'Terminale'
                                ? t('bac')
                                : levelName === 'Troisième'
                                  ? t('brevet')
                                  : ''
                        }
                        exoIndex={exoIndex}
                        quesNum={quesNum}
                    />
                ) : null}
                <DialogExoQuestionsFromFile
                    exoIndex={exoIndex}
                    quesNum={quesNum}
                />
                <Button
                    variant="link"
                    className="flex flex-col hover:no-underline gap-1 h-fit"
                    onClick={() =>
                        addQuestions(exoIndex, quesNum, [
                            { content: '', solution: '' }
                        ])
                    }
                >
                    <div className="rounded-full flex justify-center items-center border p-1 border-dinoBotVibrantBlue/30 bg-dinoBotWhite/80">
                        <FilePenLine className="size-7" />
                    </div>
                    <span>{t('custom')}</span>
                </Button>
            </div>
        </div>
    )
}

export default QuestionContent
