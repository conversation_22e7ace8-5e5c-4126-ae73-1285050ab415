import React, { useEffect, useRef, useState } from 'react'
import { X } from 'lucide-react'
import 'react-quill-new/dist/quill.snow.css'
import { Button } from '@dinobot/components-ui'
import useCortexStore from '../store/control-cortex-store'

// Dynamic import for MathfieldElement
let MathfieldElement: any = null
if (typeof window !== 'undefined') {
    import('mathlive').then(({ MathfieldElement: MathfieldElementClass }) => {
        MathfieldElement = MathfieldElementClass
        MathfieldElement.fontsDirectory = '/mathlive-fonts'
        MathfieldElement.soundsDirectory = null
    })
}

function ControlCortexDialog() {
    const [visible, setVisible] = useState(false)
    const [mathfieldReady, setMathfieldReady] = useState(false)
    const mf = useRef<any>(null)
    const mfp = useRef<HTMLDivElement>(null)

    const {
        closeCortexDialog,
        open,
        insertNow,
        setCortexValue,
        cortexValue,
        idanswer,
        idcontent
    } = useCortexStore()

    useEffect(() => {
        if (!MathfieldElement || !mathfieldReady) {
            // Wait for MathfieldElement to be available
            const checkMathfield = setInterval(() => {
                if (MathfieldElement) {
                    setMathfieldReady(true)
                    clearInterval(checkMathfield)
                }
            }, 100)
            return () => clearInterval(checkMathfield)
        }

        const El = new MathfieldElement()
        mf.current?.appendChild(El as Node)
        mf.current?.focus()

        if (typeof window !== 'undefined' && (window as any).mathVirtualKeyboard) {
            (window as any).mathVirtualKeyboard.container = mfp.current
        }

        if (open) {
            setVisible(true)
            ;(window as any).mathVirtualKeyboard?.show()
        } else {
            setVisible(false)
            ;(window as any).mathVirtualKeyboard?.hide()
        }
        mfp.current
            ?.querySelectorAll('[data-tooltip="Lettres romaines"]')
            .forEach(e => e.remove())
    }, [open, mathfieldReady])

    return (
        <div
            className={`${open ? 'flex' : 'hidden'} bg-slate-100 border-2 border-dinoBotDarkGray rounded-sm flex-col justify-between gap-2 absolute top-1/2 -translate-x-1/2 left-1/2 -translate-y-1/2 size-fit p-2 ${visible ? 'h-80' : 'h-fit'}`}
        >
            <div className="flex h-fit">
                <div className="w-96">
                    {/* @ts-expect-error Explanation: main-field dont have a import */}
                    <math-field
                        ref={mf}
                        onInput={(evt: React.ChangeEvent<any>) =>
                            setCortexValue(
                                evt.target.value,
                                idanswer,
                                idcontent
                            )
                        }
                        style={{ width: '100%' }}
                    >
                        {cortexValue[idanswer]?.[idcontent] || ''}
                        {/* @ts-expect-error Explanation: main-field dont have a import */}
                    </math-field>
                </div>
                <div className="size-full sm:w-fit flex flex-row-reverse sm:flex-row justify-center items-center gap-2">
                    <Button
                        className="bg-dinoBotRed text-white hover:bg-dinoBotRed/80 transition-all duration-200 rounded h-10"
                        onClick={insertNow}
                    >
                        Insérer
                    </Button>
                    <Button
                        className="h-10"
                        variant={'outline'}
                        onClick={closeCortexDialog}
                    >
                        <X />
                    </Button>
                </div>
            </div>
            <div
                ref={mfp}
                className={`${visible ? 'h-72' : 'h0'} w-[530px]`}
            ></div>
        </div>
    )
}

export default ControlCortexDialog
