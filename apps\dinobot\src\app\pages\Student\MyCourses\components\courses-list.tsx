import React from 'react'
import CourseCard from './course-card'
import { InfoIcon } from 'lucide-react'
import CourseDialog from './course-dialog'
import { useTranslation } from 'react-i18next'
import useMyCourses from '../hooks/useMyCourses'
import { ScrollArea } from '@radix-ui/react-scroll-area'
import { Button } from '@dinobot/components-ui'

function CoursesList() {  
    const { t } = useTranslation(['app/courses/index'])

    const { courses } = useMyCourses()

    return (
        <ScrollArea className="h-3/4 w-11/12 bg-white border-dinoBotWhite border p-4 m-4 rounded-sm">
            <div className="flex justify-between">
                <div className="flex gap-2">
                    <h2 className="font-bold">{t('title')}</h2>
                    <InfoIcon />
                </div>
                <CourseDialog
                    trigger={
                        <Button
                            variant="link"
                            className="text-dinoBotBlue underline"
                        >
                            {t('dialog.trigger')}
                        </Button>
                    }
                />
            </div>

            <div className="flex flex-wrap mt-2">
                {courses &&
                    courses.map((item, index) => (
                        <CourseCard key={index} {...item} />
                    ))}
            </div>
        </ScrollArea>
    )
}

export default CoursesList
