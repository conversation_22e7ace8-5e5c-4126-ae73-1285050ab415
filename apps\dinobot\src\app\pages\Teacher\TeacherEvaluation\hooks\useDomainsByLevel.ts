import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { Domain } from '@dinobot/prisma'

export const useDomainsByLevel = (levelId: number | null | undefined) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['domains-by-level', levelId],
        queryFn: async (): Promise<Domain[]> => {
            if (!levelId || !apiClient) return []

            const response = await apiClient.get(`/api/domains/level/${levelId}`)
            return response.data
        },
        enabled: !!levelId && !!apiClient
    })
}
