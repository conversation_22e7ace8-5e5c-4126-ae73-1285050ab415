import { create } from 'zustand'
import {
  CreateControleState,
  CreateControleActions,
  CtrlInfo
} from '../CreateControle.types'
import { createSelectors } from '@dinobot/stores'

const initialCtrlInfo: CtrlInfo = {
  chapterId: '',
  partId: ''
}

const initialState: CreateControleState = {
  subject: null,
  ctrlInfo: initialCtrlInfo,
  time: new Date(new Date().setHours(0, 0, 0, 0)),
  chapters: [],
  parts: [],
  isLoading: false,
  error: null,
  selectedChapter: null,
  showError: false
}

export const useCreateControleStore = create<CreateControleState & CreateControleActions>((set, get) => ({
  ...initialState,

  setSubject: (subject) => set({ subject }),

  updateCtrlInfo: (field, value) => set((state) => ({
    ctrlInfo: { ...state.ctrlInfo, [field]: value }
  })),

  setTime: (time) => set({ time }),

  setChapters: (chapters) => set({ chapters }),

  setParts: (parts) => set({ parts }),

  setIsLoading: (isLoading) => set({ isLoading }),

  setError: (error) => set({ error }),

  setSelectedChapter: (selectedChapter) => set({ selectedChapter }),

  setShowError: (showError) => set({ showError }),

  reset: () => set(initialState)
}))

// Selectors for better performance
export const useCreateControleSelectors = createSelectors(useCreateControleStore)
