import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext';

export const usePartsByChapter = (chapterId: string | undefined) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['parts', chapterId],
        queryFn: async () => {
            if (!chapterId || !apiClient) return []

            const response = await apiClient.get(`/api/training-mode/parts/${chapterId}`)
            return response.data
        },
        enabled: !!chapterId && !!apiClient
    })
}
