import { ClassWithPartialRelations } from '@dinobot/prisma'
import React from 'react'
import { useNavigate } from 'react-router-dom'

function CourseCard(course: ClassWithPartialRelations) {
    const navigate = useNavigate()

    const handleClick = () => {
        navigate(`/my-courses/${course.id}`)
    }

    return (
        <div
            className="bg-dinoBotBlue h-fit w-[300px] p-2 m-2 rounded-sm font-medium text-dinoBotWhite text-xl flex gap-1 items-start flex-col hover:cursor-pointer class-color hover:opacity-80"
            onClick={handleClick}
            style={{ backgroundColor: course.classColor! }}
        >
            <div className="flex gap-0.5 items-center text-xs">
                <p>{course.level?.name}</p>
                {course.domain ? (
                    <>
                        <p>&#8226;</p>
                        <p>{course.domain?.name}</p>
                    </>
                ) : null}
            </div>
            <h2 className="text-lg">{course.name}</h2>
            <h3 className="text-xs">
                {course.mainTeacher?.lastName} {course.mainTeacher?.firstName}
            </h3>
        </div>
    )
}

export default CourseCard
