#!/bin/sh
set -e

echo "🦕 DinoBot Docker Container Starting..."

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "❌ ERROR: DATABASE_URL environment variable is not set!"
    echo "   Please provide a valid PostgreSQL connection string."
    echo "   Example: DATABASE_URL='postgresql://user:pass@host:port/dbname'"
    exit 1
fi

echo "✅ DATABASE_URL is configured"
echo "🔄 Running Prisma migrations..."

# Run Prisma migrations
if pnpm nx prisma-migrate @dinobot/prisma; then
    echo "✅ Prisma migrations completed successfully"
else
    echo "❌ Prisma migrations failed"
    exit 1
fi

echo "🚀 Starting production services..."

# Start backend in background (using built files)
echo "🔧 Starting backend on port 3001..."
cd apps/dinobot-backend/dist && PORT=3001 node app.js &

# Start frontend in background (serving static files)
echo "🎨 Starting frontend on port 4100..."
serve -s dist/apps/dinobot -p 4100 -H 0.0.0.0 &

# Wait for all background processes
wait