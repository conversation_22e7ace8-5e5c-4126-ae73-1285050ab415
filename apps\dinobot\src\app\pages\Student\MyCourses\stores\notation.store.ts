
import { PlannedEvaluationPartialWithRelations } from '@dinobot/prisma'
import { createSelectors } from '@dinobot/stores'
import { create } from 'zustand'

type NotationStoreType = NotationStateType & NotationActionType
type NotationStateType = {
    plannedEvaluations: PlannedEvaluationPartialWithRelations[]
    selectedPlannedEvaluations: PlannedEvaluationPartialWithRelations | null
}
type NotationActionType = {
    setPlannedEvaluations: (
        plannedEvaluations: PlannedEvaluationPartialWithRelations[]
    ) => void
    setSelectedPlannedEvaluations: (
        plannedEvaluation: PlannedEvaluationPartialWithRelations
    ) => void
}
const notationStateInitial: NotationStateType = {
    plannedEvaluations: [],
    selectedPlannedEvaluations: null
}

export const useNotationStore = create<NotationStoreType>()(set => ({
    ...notationStateInitial,
    setPlannedEvaluations: (
        plannedEvaluations: PlannedEvaluationPartialWithRelations[]
    ) => set({ plannedEvaluations }),
    setSelectedPlannedEvaluations: (
        selectedPlannedEvaluations: PlannedEvaluationPartialWithRelations
    ) => set({ selectedPlannedEvaluations }),
}))

export const selectorNotationStore = createSelectors(useNotationStore)
