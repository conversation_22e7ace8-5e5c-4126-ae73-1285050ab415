import { useSearchParams, useNavigate, useLocation } from 'react-router-dom'
import React from 'react'
import AboutCourse from './about/about-course'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import EditeurCalendar from './calendar/calendar'
import Scoring from './notation/scoring'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@dinobot/components-ui'

type CourseDetailsProps = {
    courseId: string
}

function CourseDetails({ courseId }: CourseDetailsProps) {
    const [searchParams, setSearchParams] = useSearchParams()
    const navigate = useNavigate()
    const location = useLocation()
    const { t, i18n } = useTranslation(['app/courses/index'])
    const dir = getLangDir(i18n.language)

    const activeTab = searchParams.get('tab') || 'about'

    // Function to change tab and update URL
    const handleTabChange = (tab: string) => {
        const newSearchParams = new URLSearchParams(searchParams)
        newSearchParams.set('tab', tab)
        setSearchParams(newSearchParams)
    }

    return (
        <Tabs
            defaultValue={activeTab}
            onValueChange={handleTabChange}
            className="bg-[#fafafa] h-4/5 w-3/4 px-6 py-2 mx-4 my-1 rounded-sm overflow-hidden"
            dir={dir}
        >
            <TabsList className="bg- hover">
                <TabsTrigger
                    value="about"
                    className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
                >
                    {t('tabs.about')}
                </TabsTrigger>
                {/* <TabsTrigger
                    value="calendar"
                    className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
                >
                    {t('tabs.calendar')}
                </TabsTrigger> */}
                <TabsTrigger
                    value="scoring"
                    className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
                >
                    {t('tabs.scoring')}
                </TabsTrigger>
            </TabsList>
            <TabsContent value="about">
                <AboutCourse courseId={courseId} />
            </TabsContent>
            {/* <TabsContent value="calendar" className="h-full">
                <EditeurCalendar />
            </TabsContent> */}
            <TabsContent value="scoring" className="h-full">
                <Scoring />
            </TabsContent>
        </Tabs>
    )
}

export default CourseDetails
