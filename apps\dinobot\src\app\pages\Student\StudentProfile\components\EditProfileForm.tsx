import React, { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { IconSpinner,
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,Popover,
    PopoverContent,
    PopoverTrigger,
    SelectValue, Button , Calendar ,Input } from '@dinobot/components-ui'
import { GENDER_KEYS ,
    formatDateForLocale,
    getLangProps,
    locales,
    parseDateString
} from '@dinobot/utils'
import { Level } from '@dinobot/prisma'

import { CalendarIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { useStudentProfile } from '../hooks/useStudentProfile'
import { useStudentProfileStore } from '../store/StudentProfile.store'

function isValidDate(date: Date | undefined) {
    if (!date) {
        return false
    }
    return !isNaN(date.getTime())
}

export default function EditProfileForm() {
    const navigate = useNavigate()
    const { user, setIsEditingProfile } = useStudentProfileStore()
    const { updateProfile, isUpdatingProfile, levels, isLoadingLevels } = useStudentProfile()
    const [gender, setGender] = useState(user?.gender)
    const [level, setLevel] = useState(user?.userLevel?.id)
    const [birthDate, setBirthDate] = useState<Date | undefined>(
        user.birthDate ? new Date(user.birthDate) : undefined
    )
    const {t,i18n} = useTranslation(['app/chat'],{keyPrefix: 'profile.profil'})
    const locale = i18n.language 
    const [month, setMonth] = useState<Date | undefined>(birthDate)
    const [value, setValue] = useState(formatDateForLocale(birthDate, locale))
    const [open, setOpen] = useState(false)
    const [formData, setFormData] = useState({
        firstName: user?.firstName || '',
        lastName: user?.lastName || '',
        gender: user?.gender || ''
    })


    const genderOptions = GENDER_KEYS.map(key => ({
        key,
        label: t(`genders.${key}`)
    }))

    const handleLevelChange = (value: number) => {
        setLevel(value)
    }

    const handleGenderChange = (value: string) => {
        setGender(value)
    }

    const getLevelName = () => {
        return levels?.find(lvl => lvl.id === level)?.name
    }

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        
        const formDataToSubmit = {
            id: user?.id,
            firstName: formData.firstName,
            lastName: formData.lastName,
            gender: gender || '',
            levelId: level,
            birthDate: birthDate?.toISOString()
        }

        try {
            const result = await updateProfile(formDataToSubmit)
            if (result.type === 'success') {
                toast.success(t('profile-updated'))
                setIsEditingProfile(false)
            } else {
                toast.error(t('error-update'), {
                    description: (
                        <span className="text-dinoBotBlue text-xs">
                            {result.message}
                        </span>
                    )
                })
            }
        } catch (error) {
            toast.error(t('error-update'))
        }
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target
        setFormData(prev => ({ ...prev, [name]: value }))
    }

    return (
        <form
            onSubmit={handleSubmit}
            className="flex flex-col items-center gap-4 space-y-"
        >
            <div>
                <div className="flex flex-col">
                    <div className="flex flex-col md:grid md:grid-cols-2 md:gap-4 mb-4">
                        <div className="flex flex-col">
                            <div className="flex font-bold mb-2">
                                {t('nom')}
                            </div>
                            <div className="relative animate-fade-in-top-left mb-4 md:mb-auto">
                                <input
                                    className="peer block w-full rounded-md border bg-zinc-50 px-2 py-[9px] text-sm outline-none placeholder:text-zinc-500 dark:border-zinc-800 dark:bg-zinc-950"
                                    id="lastName"
                                    type="text"
                                    name="lastName"
                                    placeholder={t('enter-name')}
                                    value={formData.lastName}
                                    onChange={handleInputChange}
                                    required
                                />
                            </div>
                        </div>
                        <div className="flex flex-col">
                            <div className="flex font-bold mb-2">
                                {t('prenom')}
                            </div>
                            <div className="relative animate-fade-in-top-right mb-4 md:mb-auto">
                                <input
                                    className="peer block w-full rounded-md border bg-zinc-50 px-2 py-[9px] text-sm outline-none placeholder:text-zinc-500 dark:border-zinc-800 dark:bg-zinc-950"
                                    id="firstName"
                                    type="text"
                                    name="firstName"
                                    placeholder={t('entre-prenom')}
                                    value={formData.firstName}
                                    onChange={handleInputChange}
                                    required
                                />
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col md:grid md:grid-cols-2 md:gap-4 mb-2">
                        <div className="flex flex-col">
                            <div className="flex font-bold mb-2">
                                {t('genre')}
                            </div>
                            <div className="relative animate-fade-in-bottom-left mb-4 md:mb-auto">
                                <Select
                                    value={gender!}
                                    onValueChange={handleGenderChange}
                                >
                                    <SelectTrigger>
                                        <SelectValue
                                            placeholder={t('select-genre')}
                                        />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectGroup>
                                            {genderOptions.map(
                                                ({ key, label }) => (
                                                    <SelectItem
                                                        key={key}
                                                        value={key}
                                                    >
                                                        {label}
                                                    </SelectItem>
                                                )
                                            )}
                                        </SelectGroup>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div className="flex flex-col">
                            <div className="flex font-bold mb-2">
                                {t('niveau')}
                            </div>
                            <div className="relative animate-fade-in-bottom-right mb-4 md:mb-auto">
                                <Select
                                    value={level?.toString()}
                                    onValueChange={e =>
                                        handleLevelChange(Number(e))
                                    }
                                >
                                    <SelectTrigger>
                                        <SelectValue
                                            placeholder={t('select-niveau')}
                                        />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectGroup>
                                            {levels?.map(level => (
                                                <SelectItem
                                                    key={level.id}
                                                    value={level.id.toString()}
                                                >
                                                    {getLangProps({
                                                        obj: level,
                                                        base: 'name',
                                                        lang: locale})}
                                                </SelectItem>
                                            )) || []}
                                        </SelectGroup>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col mb-4">
                        <div className="flex font-bold mb-2">
                            {t('birth-date')}
                        </div>
                        <div className="relative animate-fade-in mb-4 md:mb-auto">
                            <div className="relative flex gap-2">
                                <Input
                                    id="birthDate"
                                    value={value}
                                    placeholder={t('enter-birth-date')}
                                    className="bg-zinc-50 dark:bg-zinc-950 pr-10"
                                    onChange={e => {
                                        const inputValue = e.target.value
                                        setValue(inputValue)

                                        // Utiliser notre fonction de parsing robuste
                                        const parsedDate = parseDateString(
                                            inputValue,
                                            locale
                                        )
                                        if (
                                            parsedDate &&
                                            isValidDate(parsedDate)
                                        ) {
                                            setBirthDate(parsedDate)
                                            setMonth(parsedDate)
                                        }
                                    }}
                                    onKeyDown={e => {
                                        if (e.key === 'ArrowDown') {
                                            e.preventDefault()
                                            setOpen(true)
                                        }
                                    }}
                                />
                                <Popover open={open} onOpenChange={setOpen}>
                                    <PopoverTrigger asChild>
                                        <Button
                                            id="date-picker"
                                            variant="ghost"
                                            className="absolute right-2 top-1/2 size-6 -translate-y-1/2"
                                        >
                                            <CalendarIcon className="size-3.5" />
                                            <span className="sr-only">
                                                {t('select-birth-date')}
                                            </span>
                                        </Button>
                                    </PopoverTrigger>
                                    <PopoverContent
                                        className="w-auto overflow-hidden p-0"
                                        align="end"
                                        alignOffset={-8}
                                        sideOffset={10}
                                    >
                                        <Calendar
                                            locale={locales[locale]}
                                            mode="single"
                                            selected={birthDate}
                                            captionLayout="dropdown"
                                            month={month}
                                            onMonthChange={setMonth}
                                            onSelect={date => {
                                                setBirthDate(date)
                                                setValue(
                                                    formatDateForLocale(
                                                        date,
                                                        locale
                                                    )
                                                )
                                                setOpen(false)
                                            }}
                                            disabled={date =>
                                                date > new Date() ||
                                                date <
                                                    new Date('1900-01-01')
                                            }
                                        />
                                    </PopoverContent>
                                </Popover>
                            </div>
                            {birthDate && (
                                <input
                                    type="hidden"
                                    name="birthDate"
                                    value={birthDate.toISOString()}
                                />
                            )}
                        </div>
                    </div>
                </div>
                <SaveButton isLoading={isUpdatingProfile} />
            </div>
        </form>
    )
}

function SaveButton({ isLoading }: { isLoading: boolean }) {
    const {t} = useTranslation(['app/chat'],{keyPrefix: 'profile.profil'})

    return (
        <button
            type="submit"
            className={`flex flex-row justify-center items-center my-4 h-10 w-full bg-transparent border border-dinoBotBlue rounded-2xl text-dinoBotBlue hover:bg-dinoBotBlue hover:text-white transition-all duration-300 ${!isLoading ? '' : 'opacity-30'} animate-fade-in-down`}
            aria-disabled={isLoading}
            disabled={isLoading}
        >
            {isLoading ? <IconSpinner /> : t('update-info')}
        </button>
    )
}