import React from 'react';
import { useTranslation } from 'react-i18next';
import StripeComponent from './components/Payment/StripeComponent';
import { SubscriptionStatus } from '@prisma/client';
import { useStudentProfileStore } from './store/StudentProfile.store';
import { useQuery } from '@tanstack/react-query';
import { useAuthApiClient } from '../../../contexts/AppContext';

const Payment: React.FC = () => {
    const { t } = useTranslation(['app/chat']);
    const { user } = useStudentProfileStore();
    const authApiClient = useAuthApiClient();

    // Get actual subscription status from API
    const { data: subscriptionData } = useQuery({
        queryKey: ['userSubscriptionStatus'],
        queryFn: async () => {
            const response = await authApiClient.get('/api/stripe/subscription-status')
            return response.data
        },
        staleTime: 30 * 1000, // 30 seconds
    })

    const subscriptionStatus: SubscriptionStatus = subscriptionData?.status || 'trialing'
    const trialEnd = subscriptionData?.trialEnd ? new Date(subscriptionData.trialEnd) : 
                    (user.trialEnd ? new Date(user.trialEnd) : undefined)

    return (
        <div className="w-full flex flex-col justify-start overflow-y-auto">
            <div className="py-8 px-10 flex gap-3 items-center">
                <div className="text-4xl font-bold text-gray-700">
                    {t('profile.paiement.title')}
                </div>
            </div>
            <div className="max-w-full px-10 flex items-center">
                <div className="md:w-[70%] flex flex-col px-10 overflow-y-auto">
                    <StripeComponent 
                        subStatus={subscriptionStatus}
                        trialEnd={trialEnd}
                    />
                </div>
            </div>
        </div>
    );
};

export default Payment;