import { AvatarFloat, Calculator, CalculatorTrigger, withCheck<PERSON><PERSON> } from '@dinobot/components-ui'
import { User, UserTypeSchema } from '@dinobot/prisma'
import { cn } from '@dinobot/utils'
import { Outlet, useRouteLoaderData } from 'react-router-dom'
import { useAuth } from '../../../contexts/AuthContext'
import EstablishmentSideBar from '../../../layouts/SideBars/establishment-sidebar'
import StudentSidebar from '../../../layouts/SideBars/student-sidebar'
import { Header } from '../../../layouts/header/header'

const ModeLayout = () => {
    const HEYGEN_AVATAR_KEY = import.meta.env.APP_HEYGEN_AVATAR_KEY || ''
    const HEYGEN_VOICE_ID = import.meta.env.APP_HEYGEN_VOICE_ID || ''
    const LoginMode =
        import.meta.env.APP_LOGIN_TYPE ===
        'dinobot'
            ? true
            : false
    const {user}= useRouteLoaderData('user') as {user:User}
    const { logout } = useAuth()
    const disconnect = async () => {
        await logout()
    }
    return (
        <div className="flex flex-row w-screen h-screen overflow-hidden relative">
            <div className="bg-dinoBotBlue flex flex-col z-10">
                {user?.type === UserTypeSchema.enum.establishment ? (
                    <EstablishmentSideBar
                        user={user as User}
                        logOut={disconnect}
                    />
                ) : (
                    <StudentSidebar />
                )}
            </div>
            <div className="flex flex-col size-screen grow">
                {/* <LoadingBar /> */}
                <div
                    className={cn(
                        'sticky top-0 z-0 grow',
                        LoginMode ? 'max-h-28' : 'max-h-16'
                    )}
                >
                    <Header loginType={LoginMode} />
                </div>
                <main className="flex flex-col flex-1 bg-muted/50 h-[calc(100vh-115px)] overflow-y-auto app-scroller ">
                    <div
                        className={`relative flex ${LoginMode ? 'h-[calc(100vh_-_theme(spacing.28))]' : 'h-screen'}`}
                    >
                        <AvatarFloat
                            avatarKey={HEYGEN_AVATAR_KEY}
                            voiceId={HEYGEN_VOICE_ID}
                        />
                        <Outlet />
                    </div>
                </main>
                <CalculatorTrigger />
                <Calculator />
            </div>
        </div>
    )
}

export default withCheckRole([
    UserTypeSchema.enum.student,
    UserTypeSchema.enum.establishment
])(ModeLayout)