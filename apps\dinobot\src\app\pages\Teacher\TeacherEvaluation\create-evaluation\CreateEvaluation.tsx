import { Settings } from 'lucide-react'
import React from 'react'
import ParamEvaluation from './components/param-evaluation'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'

const CreateEvaluation = () => {
    const { t, i18n } = useTranslation(['teacher.myClass.evaluation.params'])
    const dir = getLangDir(i18n.language)

    return (
        <div className="size-full flex flex-col py-11 gap-8" dir={dir}>
            <div>
                <p className="text-xl font-bold text-dinoBotGray/90 flex gap-2 items-center">
                    <Settings /> {t('title')}
                </p>
            </div>
            <ParamEvaluation isUpdate={false} />
        </div>
    )
}

export default CreateEvaluation
