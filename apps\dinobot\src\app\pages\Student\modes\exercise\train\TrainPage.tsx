import Cookies from 'js-cookie'
import { TrainChat } from './components/training-chat'
import { Session } from '@dinobot/utils'
import { redirect } from 'react-router-dom'

export default function TrainChatPage() {
    const session = JSON.parse(Cookies.get('session') || '{}') as Session


    if (!session?.user) {
        redirect(`/login`)
    }

    return (
        <TrainChat
            session={session}
        />
    )
}
