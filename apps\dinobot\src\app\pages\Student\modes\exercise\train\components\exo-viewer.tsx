import React from 'react'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypemathjax from 'rehype-mathjax'
import rehypeRaw from 'rehype-raw'

import { MathJax, MathJax3Config, MathJaxContext } from 'better-react-mathjax'
import { MemoizedReactMarkdown } from '@dinobot/components-ui'
import { cn } from '@dinobot/utils'

interface QuestionViewerProps {
    value: string
}

function QuestionViewer({ value }: QuestionViewerProps) {
    const MathJaxConfig: MathJax3Config = {
        loader: { load: ['[tex]/mhchem'] },
        tex: {
            inlineMath: [['$', '$']],
            displayMath: [['$$', '$$']],
            packages: {
                '[+]': ['mhchem']
            }
        }
    }
    return (
        <MathJaxContext config={MathJaxConfig}>
            <MathJax>
                <MemoizedReactMarkdown
                    remarkPlugins={[remarkGfm, remarkMath]}
                    rehypePlugins={[rehypemathjax, rehypeRaw]}
                    components={{
                        p({ children }) {
                            return (
                                <p
                                    className={cn(
                                        '  prose prose-red break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0',
                                        'mb-2 last:mb-0 '
                                    )}
                                >
                                    {children}
                                </p>
                            )
                        }
                    }}
                >
                    {value}
                </MemoizedReactMarkdown>
            </MathJax>
        </MathJaxContext>
    )
}

export default QuestionViewer
