import { User } from '@dinobot/prisma/lib/generated/zod/modelSchema/UserSchema';
import { Session, UserType } from '@dinobot/utils/lib/shared-types';
import Cookies from 'js-cookie';
import TeacherSideBar from '../../layouts/SideBars/teacher-sidebar';
import { Outlet, useNavigate } from 'react-router-dom';
import withCheckRole from '@dinobot/components-ui/lib/hoc/with-check-role';

function TeacherLayout() {
  const session = JSON.parse(Cookies.get('session') || '{}') as Session;
  const user = session.user;
  const navigate = useNavigate();

  if (!user) {
    navigate('/');
  }

  const disconnect = async () => {
    // await signOut()
    // redirect({ href: '/' })
  };

  return (
    <div className="flex flex-row w-screen h-screen overflow-hidden bg-[#FAFAFA]">
      <div className="w-fit z-10">
        <TeacherSideBar user={user as User} logOut={disconnect} />
      </div>
      <div className="grow flex flex-col relative">
        <Outlet />
      </div>
    </div>
  );
}

export default withCheckRole([UserType.TEACHER])(TeacherLayout);
