import { useMutation } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { EvaluationType } from '@prisma/client'
import type { 
    GenerateQuestionInput,
    GenerateQuestionsParams,
    GeneratedQuestionResponse,
    GenerateQuestionsResponse
} from '../teacherEvaluation.types'

export const useGenerateQuestions = () => {
    const apiClient = useAuthApiClient()

    return useMutation<GenerateQuestionsResponse, Error, GenerateQuestionsParams>({
        mutationFn: async (params) => {
            if (!apiClient) {
                throw new Error('API client not available')
            }

            const response = await apiClient.post('/api/training-mode/questions/generate', params)
            return response.data
        }
    })
}
