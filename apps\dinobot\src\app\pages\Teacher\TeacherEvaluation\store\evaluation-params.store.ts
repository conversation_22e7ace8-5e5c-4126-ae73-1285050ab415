import { Domain, Level, Chapter, Part, ControlExercisePartialWithRelations, ControlQuestionPartialWithRelations, ControlWithPartialRelations, ThemePartial, ControlQuestionMedia, ChapterWithPartialRelations } from '@dinobot/prisma'
import { create } from 'zustand'
import { createSelectors } from '@dinobot/stores'
import type { 
    EvaluationParams,
    EvaluationParamsType,
    EvaluationParamsActions
} from '../teacherEvaluation.types'

const initEvaluationParam: EvaluationParamsType = {
    isOnLoadExo: true,
    evalProps: null,
    exos: [{ questions: [{}] }],
    isUpdate: false,
    domains: [],
    themes: [],
    statement: [''],
    hasStatement: [false],
    mediaCount: 0,
    questionMedias: []
}

export const useEvaluationParamsStore = create<EvaluationParams>(
    (set, get) => ({
        ...initEvaluationParam,
        setIsOnLoadExo: isOnLoadExo => set({ isOnLoadExo }),
        setEvalProps: evalProps => set({ evalProps }),
        updateEvalProps: evalProps =>
            set(state => ({ evalProps: { ...state.evalProps, ...evalProps } })),
        setLevelAndDomain: (level, domain) => set({ level, domain }),
        setQuestionMedias: questionMedias => set({ questionMedias }),
        setChapter: chapter => set({ chapter }),
        setPart: part => set({ part }),
        setExos: exos => set({ exos }),
        addExo: exo => set(state => ({ exos: [...state.exos, exo] })),
        addQuestions: (exoIndex, questionIndex, questions) => {
            const ques = get()
                .exos.at(exoIndex)
                ?.questions?.filter((_, i) => i !== questionIndex)
            set(state => ({
                exos: state.exos.map((e, i) =>
                    i === exoIndex
                        ? { ...e, questions: [...ques!, ...questions] }
                        : e
                ),
                statement: [...state.statement, ''],
                hasStatement: [...state.hasStatement, false]
            }))
        },
        setStatement: (exoIndex, statement) =>
            set(state => ({
                exos: state.exos.map((e, i) =>
                    i === exoIndex ? { ...e, statement } : e
                ),
                statement: [
                    ...state.statement.map((s, i) =>
                        i === exoIndex ? statement : s
                    )
                ]
            })),
        setHasStatement: (exoIndex, hasStatement) =>
            set(state => ({
                exos: state.exos.map((e, i) =>
                    i === exoIndex ? { ...e, hasStatment: hasStatement } : e
                ),
                hasStatement: [
                    ...state.hasStatement.map((s, i) =>
                        i === exoIndex ? hasStatement : s
                    )
                ]
            })),
        removeExo: exoIndex => {
            set(state => ({
                exos: state.exos.filter((_, i) => i !== exoIndex)
            }))
            if (get().exos.length < 1) {
                set(() => ({ exos: [{ questions: [{}] }] }))
            }
        },
        removeQuestion: (exoIndex, questionIndex) => {
            set(state => ({
                exos: state.exos.map((e, index) =>
                    index === exoIndex
                        ? {
                              ...e,
                              questions: e.questions?.filter(
                                  (_, i) => i !== questionIndex
                              )
                          }
                        : e
                )
            }))
            if ((get().exos.at(exoIndex)?.questions ?? []).length < 1) {
                set(state => ({
                    exos: state.exos.map((e, index) =>
                        index === exoIndex ? { ...e, questions: [{}] } : e
                    )
                }))
            }
        },
        updateQuestion: (exoIndex, questionIndex, question) => {
            set(state => ({
                exos: state.exos.map((e, index) =>
                    index === exoIndex
                        ? {
                              ...e,
                              questions: e.questions?.map((q, i) =>
                                  i === questionIndex
                                      ? { ...q, ...question }
                                      : q
                              )
                          }
                        : e
                )
            }))
        },
        updateExo: (exoIndex, exo) =>
            set(state => ({
                exos: state.exos.map((e, i) => (i === exoIndex ? exo : e))
            })),
        havePossibilityToAdd: () =>
            get().exos.at(get().exos.length - 1)!.questions !== undefined,
        setUpdating: isUpdate => set({ isUpdate }),
        setDomains: domains => set({ domains }),
        setThemes: themes => set({ themes }),
        setMediaCount: mediaCount => set({ mediaCount }),
        resetFromDb: () => set({ chapter: undefined, part: undefined }),
        reset: () => set(initEvaluationParam)
    })
)

export const selectEvaluationParamsStore = createSelectors(
    useEvaluationParamsStore
)
