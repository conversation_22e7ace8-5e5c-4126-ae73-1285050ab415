import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'

export const useClassById = (id: string | undefined) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['class-by-id', id],
        queryFn: async (): Promise<any> => {
            if (!id || !apiClient) return null
            
            const response = await apiClient.get(`/api/control-mode/services/class/${id}`)
            return response.data
        },
        enabled: !!id && !!apiClient
    })
}