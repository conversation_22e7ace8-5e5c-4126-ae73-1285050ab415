import { Button } from '@dinobot/components-ui'
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel
} from '@dinobot/components-ui'
import { Textarea } from '@dinobot/components-ui'
import { useAuthApiClient } from '../../../../../contexts/AppContext'
import React, { useEffect, useState, useTransition } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { IconInfo } from '@dinobot/components-ui'
import { useNavigate } from 'react-router-dom'
import { selectUseScheduleAssessmentStore } from '../../store/use-schedule-assessment-store'
import EvalSelected from './eval-selected'
import SettingsChrono from './settings-chrono'
import DateEval from './date-eval'
import { useParams } from 'react-router-dom'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import moment from 'moment'
import { z } from 'zod'
import { Loader2 } from 'lucide-react'
import { zodResolver } from '@hookform/resolvers/zod'
import { Class, ClassPartialSchema, ControlPartial, ControlPartialSchema, PlannedEvaluationPartialSchema, PlannedEvaluationPartialWithRelations } from '@dinobot/prisma'
import { useClassById } from '../../hooks/useClassById'
import { useAddPlannedEvaluation } from '../../hooks/useAddPlannedEvaluation'

const EvaluationFormSchema = z.object({
    availableDate: z.date(),
    dueDate: z.date(),
    timeLimit: z.object({
        hours: z.string(),
        minutes: z.string()
    }),
    endTime: z.object({
        hours: z.string(),
        minutes: z.string()
    }),
    startTime: z.object({
        hours: z.string(),
        minutes: z.string()
    }),
    retries: z.boolean(),
    control: ControlPartialSchema,
    controlId: z.string(),
    class: ClassPartialSchema,
    classId: z.string(),
    description: z.string(),
    title: z.string()
})
type EvaluationFormType = z.infer<typeof EvaluationFormSchema>
const EvaluationForm = () => {
    const { t } = useTranslation('teacher/myClass/evaluation/schedule/form')

    const [open] = useState(false)
    const form = useForm<EvaluationFormType>()
    const selectedControl =
        selectUseScheduleAssessmentStore.use.selectedControl()
    const navigate = useNavigate()
    const param = useParams<{ classId: string }>()

    const { data: classData, isLoading: isLoadingClass } = useClassById(param.classId)
    const addPlannedEvaluationMutation = useAddPlannedEvaluation()

    useEffect(() => {
        if (!selectedControl) {
            navigate(-1)
            return
        }
        
        if (classData && selectedControl) {
            form.setValue('class', classData as Class)
            form.setValue('control', selectedControl as ControlPartial)
            form.setValue('classId', classData.id)
            form.setValue('controlId', selectedControl.id ?? '')
        }
    }, [classData, selectedControl, navigate, form])
    const onSubmit: SubmitHandler<EvaluationFormType> = async (data) => {
        try {
            const availableDate = moment(data.availableDate)
                .hour(Number(data.startTime.hours))
                .minute(Number(data.startTime.minutes))
                .toDate()
            const dueDate = moment(data.dueDate)
                .hour(Number(data.endTime.hours))
                .minute(Number(data.endTime.minutes))

            if (moment(availableDate).second(59).isBefore(moment())) {
                throw new Error(t('past_date_error'))
            }

            if (
                moment(dueDate)
                    .second(0)
                    .isSameOrBefore(moment(availableDate).second(0))
            ) {
                throw new Error(t('date_error'))
            }

            if (
                data.timeLimit.hours === '00' &&
                data.timeLimit.minutes === '00'
            ) {
                throw new Error(t('time_limit_error'))
            }

            const formattedData: PlannedEvaluationPartialWithRelations = {
                ...data,
                availableDate: moment(availableDate).utc().toDate(),
                dueDate: moment(dueDate).utc().toDate(),
                timeLimit:
                    Number(data.timeLimit.hours) * 3600 +
                    Number(data.timeLimit.minutes) * 60,
                retries: Boolean(data.retries),
                control: {
                    ...data.control,
                    status: 'ASSIGNED'
                }
            }

            const result = await addPlannedEvaluationMutation.mutateAsync(formattedData)
            const validation = PlannedEvaluationPartialSchema.safeParse(result)

            if (validation.success) {
                toast.success(t('evaluation_scheduled_success'))
                navigate(-1)
            } else {
                toast.error('Validation failed: ' + validation.error)
                console.error('Validation failed:', validation.error)
            }
        } catch (error) {
            console.error('Error in onSubmit:', error)
            toast.error(
                error instanceof Error
                    ? error.message
                    : 'An error occurred while submitting the form'
            )
        }
    }

    return (
        <div className="border border-dinoBotLightGray rounded-sm w-full h-[95%] p-4">
            <Form {...form}>
                <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="size-full"
                >
                    <div className="flex w-full h-[91%]">
                        <div className="w-7/12 flex flex-col gap-4 p-4">
                            {/* <FormField
                                name='title'
                                control={form.control}
                                rules={{required:{value:true,message:t('name_error')},}}
                                render={({field})=>(
                                    <FormItem className=' flex items-center space-y-1 w-full gap-4'>
                                        <FormLabel className='min-w-48 text-left'>{t('name')}</FormLabel>
                                        <FormControl className='flex-1'>
                                            <Input {...field} value={field.value!} placeholder={t('name_placeholder')} className="h-10  p-2 border border-gray-300 rounded-md overflow-hidden"/>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            /> */}
                            <FormField
                                name="description"
                                control={form.control}
                                render={({ field }) => (
                                    <FormItem className=" flex items-center space-y-1 w-full gap-4">
                                        <FormLabel className="min-w-48 text-left">
                                            {t('descript')}
                                        </FormLabel>
                                        <FormControl className="flex-1">
                                            <Textarea
                                                {...field}
                                                value={field.value!}
                                                placeholder={t(
                                                    'descript_placeholder'
                                                )}
                                                className="h-10  p-2 border border-gray-300 rounded-md overflow-hidden"
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />
                            <div className="">
                                <p className="flex gap-2 items-center">
                                    {t('subject')}
                                    <IconInfo fill="#5c5c5c" />
                                </p>
                                {selectedControl && !open ? (
                                    <EvalSelected />
                                ) : (
                                    <div className="border rounded-sm flex justify-around w-2/3 py-6"></div>
                                )}
                            </div>
                            <FormField
                                name="class"
                                control={form.control}
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="flex gap-2 items-center">
                                            {t('class')}
                                            <IconInfo fill="#5c5c5c" />
                                        </FormLabel>
                                        <div className="rounded-2xl flex justify-center p-2 bg-dinoBotBlue text-dinoBotWhite w-fit ">
                                            {field.value?.name}
                                        </div>
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="flex flex-col items-end w-5/12 p-4 gap-3 2xl:gap-4">
                            <FormItem>
                                <SettingsChrono control={form.control} />
                            </FormItem>
                            <FormItem>
                                <DateEval control={form.control} />
                            </FormItem>
                        </div>
                    </div>
                    <div className="flex items-center justify-center">
                        <Button
                            type="submit"
                            className="bg-dinoBotBlue hover:bg-dinoBotVibrantBlue"
                            disabled={addPlannedEvaluationMutation.isPending || isLoadingClass}
                        >
                            {addPlannedEvaluationMutation.isPending ? (
                                <>
                                    <Loader2 className="mr-2 size-4 animate-spin" />
                                    {t('loading')}
                                </>
                            ) : (
                                t('submit')
                            )}
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    )
}

export default EvaluationForm
