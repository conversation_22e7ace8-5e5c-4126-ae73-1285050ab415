import React, { Dispatch } from 'react'
import { Button } from '@dinobot/components-ui'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { useCtrlModeStore } from '@dinobot/libs/stores/src'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'

type PaginationExosProps = {
    index: number
    setIndex: Dispatch<React.SetStateAction<number>>
}

const PaginationExos = ({ index, setIndex }: PaginationExosProps) => {
    const exos = useCtrlModeStore(r => r.controle)?.exercises
    const updateDate = (direction: 'left' | 'right') => {
        switch (direction) {
            case 'left':
                setIndex(i => (i <= 0 ? i : i - 1))
                break
            case 'right':
                setIndex(i => (i > exos!.length - 1 ? i : i + 1))
                break
        }
    }
    const { i18n } = useTranslation()
    const dir = getLangDir(i18n.language)
    return (
        <div className="border rounded-md px-2 flex justify-between items-center w-fit mb-2">
            <Button
                variant="link"
                className="p-2 pl-1"
                disabled={index <= 0}
                onClick={() => updateDate('left')}
            >
                {dir === 'ltr' ? <ChevronLeft /> : <ChevronRight />}
            </Button>
            <h4 className="text-lg font-bold text-dinoBotDarkGray">
                {exos?.at(index)?.title}
            </h4>
            <Button
                variant="link"
                className="p-2 pr-1"
                disabled={!exos || index >= exos.length - 1}
                onClick={() => updateDate('right')}
            >
                {dir === 'ltr' ? <ChevronRight /> : <ChevronLeft />}
            </Button>
        </div>
    )
}

export default PaginationExos
