import React from 'react';
import { useTranslation } from 'react-i18next';
import { Pencil } from 'lucide-react';
import ShowProfile from './ShowProfile';
import EditProfileForm from './EditProfileForm';
import DeleteAccountButton from './DeleteAccountButton';
import { useStudentProfileStore } from '../store/StudentProfile.store';

// Button Component
const Button: React.FC<{
    children: React.ReactNode;
    onClick?: () => void;
    variant?: 'default' | 'ghost' | 'destructive' | 'secondary';
    className?: string;
    disabled?: boolean;
    type?: 'button' | 'submit';
}> = ({ children, onClick, variant = 'default', className = '', disabled, type = 'button' }) => {
    const baseClasses = 'px-4 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
    const variantClasses = {
        default: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
        ghost: 'bg-transparent text-gray-600 hover:bg-gray-100 hover:text-gray-900',
        destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
        secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500'
    };

    return (
        <button
            type={type}
            onClick={onClick}
            disabled={disabled}
            className={`${baseClasses} ${variantClasses[variant]} ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
            {children}
        </button>
    );
};

// Separator Component
const Separator: React.FC<{ className?: string }> = ({ className = '' }) => {
    return <hr className={`border-gray-200 ${className}`} />;
};

const StudentProfileForm: React.FC = () => {
    const { t } = useTranslation(['app/chat']);
    const { user, isEditingProfile, setIsEditingProfile } = useStudentProfileStore();

    const handleEditingProfileChange = () => {
        setIsEditingProfile(!isEditingProfile);
    }

    return (
        <div className="flex flex-col gap-4">
            <div className="text-2xl text-blue-600 font-bold mb-6">
                {t('profile.profil.title')}
            </div>

            <div className="flex flex-col mb-6">
                <div className="flex font-bold">{t('profile.profil.email')}</div>
                <div className="flex">{user.email || t('profile.profil.nodefine')}</div>
            </div>

            <div className="w-full flex flex-row justify-between">
                <div className="flex font-bold text-lg mr-10">{t('profile.profil.info')}</div>
                <Button
                    className="text-gray-400 hover:text-black mb-2"
                    variant="ghost"
                    onClick={handleEditingProfileChange}
                >
                    <Pencil />
                    <span className="sr-only">{t('profile.profil.edit-profile')}</span>
                </Button>
            </div>
            <Separator className="mb-2 -mt-4" />

            {isEditingProfile ? <EditProfileForm /> : <ShowProfile />}

            <Separator className="mb-4" />

            <div className="mt-8">
                <div className="flex font-bold text-lg mb-4 text-black">
                    {t('profile.profil.delete-account-section')}
                </div>
                <Separator className="mb-4" />
                <div className="mb-4">
                    <p className="text-sm text-gray-500 mb-4">
                        {t('profile.profil.delete-account-description')}
                    </p>
                    <DeleteAccountButton />
                </div>
            </div>
        </div>
    );
};

export default StudentProfileForm;