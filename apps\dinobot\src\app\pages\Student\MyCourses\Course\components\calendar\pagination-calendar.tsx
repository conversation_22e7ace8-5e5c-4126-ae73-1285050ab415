import React from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { NavigateAction } from 'react-big-calendar'
import { getLangDir } from 'rtl-detect'
import { useTranslation } from 'react-i18next'
import { Button } from '@dinobot/components-ui'

type PaginationCalendarProps = {
    label: string
    onNavigate: (action: NavigateAction, newDate?: Date) => void
}

const PaginationCalendar = ({ label, onNavigate }: PaginationCalendarProps) => {
    const { i18n } = useTranslation()
    const dir = getLangDir(i18n.language)
    return (
        <div className="border rounded-md px-2 flex justify-between items-center w-fit mb-2">
            <Button
                variant="link"
                className="p-2 pl-1"
                onClick={() => onNavigate('PREV')}
            >
                <ChevronLeft
                    className={`${dir === 'rtl' ? 'rotate-180' : ''}`}
                />
            </Button>
            <h4 className="text-lg font-bold text-dinoBotDarkGray">{label}</h4>
            <Button
                variant="link"
                className="p-2 pr-1"
                onClick={() => onNavigate('NEXT')}
            >
                <ChevronRight
                    className={`${dir === 'rtl' ? 'rotate-180' : ''}`}
                />
            </Button>
        </div>
    )
}

export default PaginationCalendar
