import { useMutation } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext';

export const useGenerateControlQuestions = () => {
    const apiClient = useAuthApiClient()

    return useMutation({
        mutationFn: async (generatorInput: any) => {
            if (!apiClient) {
                throw new Error('API client not available')
            }

            const response = await apiClient.post('/api/control-generator/questions', generatorInput)
            return response.data
        }
    })
}
