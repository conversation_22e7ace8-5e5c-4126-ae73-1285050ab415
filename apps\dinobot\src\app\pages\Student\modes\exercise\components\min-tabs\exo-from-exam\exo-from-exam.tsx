
import React, { useEffect, useState } from 'react'
import { toast } from 'sonner'
import ExoFromExamTableLayout from './exo-from-exam-table-layout'
import { ExoOutput, ExoSortsAndFilters, ExoSubject } from '../../../trainig.types'
import { Domain } from '@dinobot/prisma'
import { selectUseExoModeStore, useExoModeStore } from '@dinobot/stores'
import { useTranslation } from 'react-i18next'
import { Button, ErrorTooltip, InfoTooltip, Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue, Slider } from '@dinobot/components-ui'
import { useExerciseData } from '../../../hooks'

type  ExoFromDbProps = {
    domains: Domain[]
    subjects: ExoSubject[]
    // getExams: (filters: ExoSortsAndFilters) => Promise<ExoOutput[]>
    onGenerate: () => Promise<void>
}

function ExoFromExamMin({
    domains,
    subjects,
    // getExams,
    onGenerate
}: ExoFromDbProps) {
    const [selectedSubject, setSelectedSubject] = useState<string | null>(null)
    const [exams, setExams] = useState<ExoOutput[]>([])
    const formData = selectUseExoModeStore.use.exoInfoFromExam()
    const handleFormChange = selectUseExoModeStore.use.updateExoInfoFromExam()
    const [showError, setShowError] = useState<boolean>(false)
    const setMode = useExoModeStore(state => state.setMode)
    const {t} = useTranslation(['app/mode'],{keyPrefix: 'exo.mini-tabs.exam'})
    const {t:t2} = useTranslation(['app/mode'],{keyPrefix: 'exo.tab.db'})
    
    // API hooks
    const { useExamsByDomainLevelSubjects } = useExerciseData()
    
    // Get exams with category filter
    const { data: examsData } = useExamsByDomainLevelSubjects(
        formData.domainId ? Number.parseInt(formData.domainId) : undefined,
        undefined, // levelId not needed for this case
        selectedSubject ? [selectedSubject] : undefined
    )

    useEffect(() => {
        setMode('FROM_EXAM')
    }, [])

    // Update exams when examsData changes
    useEffect(() => {
        if (examsData) {
            setExams(examsData)
        }
    }, [examsData])

    useEffect(() => {
        if (selectedSubject) {
            // GetExam - Now handled by React Query hook above
            // The exams will be automatically updated when examsData changes
        }
    }, [selectedSubject])

    const handleQuestionNumberChange = (value: number) => {
        try {
            // const int = parseInt(value)
            if (value > 10) {
                handleFormChange('qstNbr', 10)
                toast.info(t('n10'), { duration: 2500 })
            } else if (value < 1) {
                handleFormChange('qstNbr', 1)
                toast.info(t('n1'), { duration: 2500 })
            } else {
                handleFormChange('qstNbr', value)
            }
        } catch (error) {
            console.log(error)
            handleFormChange('qstNbr', 5)
        }
    }

    useEffect(() => {
        if (formData.qstNbr) handleQuestionNumberChange(formData.qstNbr)
    }, [formData.qstNbr])

    const submit = async () => {
        if (formData.examId) {
            if (formData.qstNbr) {
                setShowError(false)
                setMode('FROM_EXAM')
                // router.push("/train")
                await onGenerate()
            } else {
                toast.error(t('n1&10'))
            }
        } else {
            setShowError(true)
            toast.info(t('error'))
        }
    }

    const handleSortFilters = async (value: ExoSortsAndFilters) => {
        if (value) {
            // Get exam - Filtering/sorting is now handled by the React Query hook
            // For now, we use the basic hook. More complex filtering can be implemented
            // by extending the useExerciseData hook to accept filter/sort parameters
        }
    }

    return (
        <div className="flex flex-col gap-4 xl:gap-2 2xl:gap-6">
            <div>
                <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                    Chapitre{' '}
                    {!showError ||
                    !!selectedSubject ||
                    (selectedSubject && selectedSubject?.length <= 0) ? (
                        <InfoTooltip message={t('chois-chap')} />
                    ) : (
                        <ErrorTooltip message={t('error-choi-chap')} />
                    )}
                </div>
                <Select onValueChange={value => setSelectedSubject(value)}>
                    <SelectTrigger className="w-80 max-w-full">
                        <SelectValue placeholder={t('chois-chap')} />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectGroup>
                            {subjects.map(subject => (
                                <SelectItem
                                    key={subject.id}
                                    value={subject.name}
                                >
                                    {subject.name}
                                </SelectItem>
                            ))}
                        </SelectGroup>
                    </SelectContent>
                </Select>
            </div>

            {exams.length > 0 && (
                <>
                    <div className="flex flex-col gap-1">
                        <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                            Examen{' '}
                            {(formData.examId.length <= 0 && !showError) ||
                            formData.examId.length > 0 ? (
                                <InfoTooltip message={t('chois-sav')} />
                            ) : (
                                <ErrorTooltip message={t('error-chois-sav')} />
                            )}
                        </div>
                        <div>
                            <p className="text-[10px] text-[#707070]">
                                {t('chois-sav-prps')}
                            </p>
                            <p className="text-[10px] font-light text-[#707070]">
                                {exams.length} {t('res')}
                            </p>
                        </div>
                        <div className="mt-2">
                            <ExoFromExamTableLayout
                                exams={exams}
                                filtersSortChange={handleSortFilters}
                            />
                        </div>
                    </div>
                    <div className="w-full flex flex-col md:flex-row gap-4 md:gap-12">
                        {/* <div className='w-full sm:w-2/5'>
              <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                Nombre de questions
              </div>
              <Input type="number" value={formData.qstNbr} min={1} max={10} className='h-9 rounded-xl' onChange={(e)=>handleFormChange("qstNbr",e.target.value)}/>
            </div> */}
                        <div className="w-full sm:w-2/5">
                            <div className="flex gap-1 items-center justify-between text-sm font-bold text-dinoBotDarkGray">
                                {t('difficulty')}{' '}
                                <span>{formData.difficulty}/3</span>
                            </div>
                            <div className="py-3">
                                <Slider
                                    defaultValue={[formData.difficulty]}
                                    min={1}
                                    max={3}
                                    step={1}
                                    className={'w-full '}
                                    onValueChange={value =>
                                        handleFormChange('difficulty', value[0])
                                    }
                                />
                            </div>
                        </div>
                    </div>
                </>
            )}
            <div className="w-full flex justify-center items-center mt-6">
                <Button
                    className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
                    onClick={submit}
                >
                    {t('submit')}
                </Button>
            </div>
        </div>
    )
}

export default ExoFromExamMin
