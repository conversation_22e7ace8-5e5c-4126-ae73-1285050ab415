import React, { ReactNode } from 'react'
import LayoutEvaluation from '../components/layout-evaluation'

type LayoutProps = {
    children: ReactNode
    title: ReactNode
    params: Promise<{ classId: string }>
}

const layout = async (props: LayoutProps) => {
    const params = await props.params

    const { children, title } = props

    return (
        <LayoutEvaluation params={params} title={title}>
            {children}
        </LayoutEvaluation>
    )
}

export default layout
