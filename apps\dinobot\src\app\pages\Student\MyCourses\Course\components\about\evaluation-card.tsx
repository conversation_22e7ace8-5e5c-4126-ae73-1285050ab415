import { ControlPartialWithRelations } from '@dinobot/prisma'
import { Eye, Home, Timer } from 'lucide-react'
import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'
import Cookies from 'js-cookie'

type EvaluationCardProps = {
    evaluation?: ControlPartialWithRelations
    isEvalOrTask?: boolean
    plannedId?: string
    availableDate?: Date
}

const EvaluationCard = ({
    evaluation,
    isEvalOrTask = true,
    plannedId,
    availableDate
}: EvaluationCardProps) => {
    const navigate = useNavigate()
    const { t } = useTranslation(['app/courses/index'])

    return (
        <div
            className={`border-2 rounded-lg ${isEvalOrTask ? 'border-dinoBotRedOrange/60' : 'border-dinoBotVibrantBlue/60'}  w-72 min-h-28 p-2`}
        >
            <div className="flex justify-between items-center">
                <div className="flex gap-2 items-center">
                    {/* <Checkbox id="terms" className={`size-5 ${isEvalOrTask?'data-[state=checked]:bg-dinoBotVividOrange border-dinoBotRedOrange':'data-[state=checked]:bg-dinoBotVibrantBlue border-dinoBotVibrantBlue'} `}/> */}
                    <h3
                        className={`${isEvalOrTask ? 'text-dinoBotRedOrange' : 'text-dinoBotVibrantBlue'} flex gap-2`}
                    >
                        {isEvalOrTask ? (
                            <>
                                <Timer />
                                <span className="mt-auto">
                                    {t('card.evaluation')}
                                </span>
                            </>
                        ) : (
                            <>
                                <Home />
                                <span className="mt-auto">
                                    {t('card.form')}
                                </span>
                            </>
                        )}
                    </h3>
                </div>
                <Eye
                    className="cursor-pointer"
                    onClick={() => {
                        const canNotPassTest =
                            availableDate && new Date() < availableDate
                        if (canNotPassTest) {
                            toast.error('Pas disponible encore')
                            return
                        }
                        // Set feature flag for control mode and navigate
                        Cookies.set('feature', 'Ctrl')
                        navigate(`/controle/${plannedId}`)
                    }}
                />
            </div>
            <p className="text-dinoBotGray">
                {evaluation?.status == 'ASSIGNED'
                    ? ''
                    : t('card.draft')}
            </p>
            <p className="text-wrap line-clamp-3">{evaluation?.name}</p>
        </div>
    )
}

export default EvaluationCard
