import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { ControlPartialWithRelations } from '@dinobot/prisma'

export const useControlWithMedia = (id: string | undefined) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['control-with-media', id],
        queryFn: async (): Promise<ControlPartialWithRelations | null> => {
            if (!id || !apiClient) return null
            
            const response = await apiClient.get(`/api/control-mode/${id}/media`)
            return response.data
        },
        enabled: !!id && !!apiClient
    })
}