// TeacherEvaluation Types - Centralized type definitions

import type {
  Control,
  ControlExercisePartialWithRelations,
  ControlPartialWithRelations,
  ControlWithPartialRelations,
  ControlQuestionPartialWithRelations,
  ControlExercisePartialRelations,
  ControlQuestionMedia,
  ControlExerciseMedia,
  Domain,
  Level,
  Chapter,
  ChapterWithPartialRelations,
  Part,
  ThemePartial,
  Exercise,
  Exam,
  JsonValueType,
  EvaluationTypeType
} from '@dinobot/prisma'
import type * as Prisma from '@prisma/client'
import type { z } from 'zod'

// ============================================================================
// STORE TYPES
// ============================================================================

export type EvaluationParams = EvaluationParamsActions & EvaluationParamsType

export type EvaluationParamsType = {
  evalProps: ControlWithPartialRelations | null
  exos: ControlExercisePartialWithRelations[]
  isOnLoadExo: boolean
  level?: Level
  domain?: Domain
  domains: Domain[]
  chapter?: ChapterWithPartialRelations
  part?: Part
  themes: ThemePartial[]
  isUpdate: boolean
  statement: string[]
  hasStatement: boolean[]
  questionMedias: ControlQuestionMedia[][]
  mediaCount: number
}

export type EvaluationParamsActions = {
  setQuestionMedias: (questionMedias: ControlQuestionMedia[][]) => void
  setIsOnLoadExo: (isOnLoadExo: boolean) => void
  setEvalProps: (evalProps: ControlWithPartialRelations) => void
  updateEvalProps: (evalProps: ControlWithPartialRelations) => void
  setLevelAndDomain: (level: Level, domain: Domain) => void
  setDomains: (domains: Domain[]) => void
  setThemes: (themes: ThemePartial[]) => void
  setChapter: (chapter: Chapter) => void
  setPart: (part: Part) => void
  setExos: (exos: ControlExercisePartialWithRelations[]) => void
  setStatement: (exoIndex: number, statement: string) => void
  setHasStatement: (exoIndex: number, hasStatement: boolean) => void
  addExo: (exo: ControlExercisePartialWithRelations) => void
  addQuestions: (exoIndex: number, questionIndex: number, questions: ControlQuestionPartialWithRelations[]) => void
  removeExo: (exoIndex: number) => void
  removeQuestion: (exoIndex: number, questionIndex: number) => void
  updateQuestion: (exoIndex: number, questionIndex: number, question: ControlQuestionPartialWithRelations) => void
  updateExo: (exoIndex: number, exo: ControlExercisePartialWithRelations) => void
  setMediaCount: (mediaCount: number) => void
  havePossibilityToAdd: () => boolean
  setUpdating: (isUpdate: boolean) => void
  resetFromDb: () => void
  reset: () => void
}

export type ScheduleAssessmentStoreType = ScheduleAssessmentStoreAction & ScheduleAssessmentStoreState

export type ScheduleAssessmentStoreState = {
  controls: ControlPartialWithRelations[]
  selectedControl: ControlPartialWithRelations | undefined
  level: string
}

export type ScheduleAssessmentStoreAction = {
  setControls: (controls: ControlPartialWithRelations[]) => void
  setSelectedControl: (controle: ControlPartialWithRelations | undefined) => void
  setLevel: (level: string) => void
}

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseControlsParams {
  skip?: number
  take?: number
  authorId?: string
  status?: string
  scoringType?: string
}

export interface GenerateQuestionInput {
  knowledge: string
  question: string
  questionNbr: number
  solution: string
  competences: string[]
  statement: string
  exerciseType: Prisma.EvaluationType
  previousQuestions?: {
    knowledge: string
    question: string
    solution: string
    statement: string
  }[]
}

export interface GenerateQuestionsParams {
  input: GenerateQuestionInput[]
  level: { id?: number; name?: string }
  domain: { id?: number; name?: string }
  chapter: { id?: string; title?: string }
  part?: { id?: string; name?: string }
  evalType: Prisma.EvaluationType
}

export interface GeneratedQuestionResponse {
  question?: string
  solution: string
  desmosCode?: JsonValueType
}

export interface GenerateQuestionsResponse {
  output: GeneratedQuestionResponse[]
  statement: string
}

export interface SearchSimilarQuestionsParams {
  partId: string
  partName?: string
  chapterName?: string
  levelName?: string
  domainName?: string
  exerciseType?: string
}

export type CreateThemeInput = {
  name: string
  classId: string
}

// ============================================================================
// EXAM/EXERCISE TYPES (from backend)
// ============================================================================

export interface ExoOutput extends Exercise {
  exam: Exam
  subjects: { subject: { name: string } }[]
}

export type SortColumn =
  | 'exo.title'
  | 'exam.title'
  | 'exam.year'
  | 'exam.type'
  | 'exo.category'
  | 'none'

export interface ExoSortsAndFilters {
  sort: ExoSorting
  filters: ExoFilters
  pagination: ExoPagination
}

export interface ExoSorting {
  column: SortColumn
  order: 'asc' | 'desc' | undefined
}

export interface ExoPagination {
  skip?: number | undefined
  take?: number | undefined
}

export interface ExoFilters {
  exoTitle?: string | undefined
  internalTitle?: string | undefined
  examtitle?: string | undefined
  examYear?: { from: number | undefined; to: number | undefined }
  examType?: Prisma.ExamType | undefined
  domainId?: number | undefined
  levelId?: number | undefined
  domainName?: string | undefined
  exoCategory?: string[]
}

export interface Exo {
  id?: string | undefined
  title?: string | undefined
  assignment?: string | undefined
  assignmentMedia?: string | undefined
  solutionMedia?: string | undefined
}

