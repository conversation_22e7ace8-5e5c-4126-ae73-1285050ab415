import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { PlannedEvaluationPartialWithRelations } from '@dinobot/prisma'

export const useAddPlannedEvaluation = () => {
    const apiClient = useAuthApiClient()
    const queryClient = useQueryClient()

    return useMutation({
        mutationFn: async (data: PlannedEvaluationPartialWithRelations): Promise<any> => {
            if (!apiClient) throw new Error('API client not available')
            
            const response = await apiClient.post('/api/evaluation-scheduler/', data)
            return response.data
        },
        onSuccess: () => {
            // Invalidate and refetch related queries
            queryClient.invalidateQueries({ queryKey: ['planned-evaluations'] })
            queryClient.invalidateQueries({ queryKey: ['evaluations'] })
        }
    })
}