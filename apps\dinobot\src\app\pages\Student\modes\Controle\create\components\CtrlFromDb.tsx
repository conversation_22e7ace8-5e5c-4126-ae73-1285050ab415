import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';
import { useCreateControleStore } from '../stores/CreateControle.store';
import { Chapter } from '../CreateControle.types';
import { InfoTooltip } from '@dinobot/components-ui/lib/ui/info-tooltip';
import { ErrorTooltip } from '@dinobot/components-ui/lib/ui/error-tooltip';
import {
  Button,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
  TimePicker,
} from '@dinobot/components-ui';
import { getLangProps } from '@dinobot/utils';
import { useCtrlModeStore } from '@dinobot/stores';
import Cookie from 'js-cookie';
// import { Chapter } from '../create-controle.types';
// import { getLangProps } from '@/lib/utils/string.utils';

interface CtrlFromDbProps {
  chapters: Chapter[];
  getParts: (chapterId: string) => void;
  submitForm: (formData: { chapterId: string; time: Date }) => void;
  isSubmitting: boolean;
}

const CtrlFromDb: React.FC<CtrlFromDbProps> = ({
  chapters,
  getParts,
  submitForm,
  isSubmitting,
}) => {
  const { t, i18n } = useTranslation(['app/mode']);
  const locale = i18n.language;
  const { ctrlInfo, time, updateCtrlInfo, setTime, setSubject } =
    useCtrlModeStore();

  const { setShowError, showError } = useCreateControleStore();

  const [selectedChapterLocal, setSelectedChapterLocal] = useState<
    string | null
  >(null);

  useEffect(() => {
    if (selectedChapterLocal) {
      updateCtrlInfo('chapterId', selectedChapterLocal);
      getParts(selectedChapterLocal);
    }
  }, [selectedChapterLocal, updateCtrlInfo, getParts]);

  useEffect(() => {
    setSelectedChapterLocal(null);
    updateCtrlInfo('chapterId', '');
  }, [chapters, updateCtrlInfo]);

  const handleSubmit = () => {
    const isValidTime =
      time &&
      (time.getHours() > 0 || time.getMinutes() > 0 || time.getSeconds() > 0);
    const topic = Cookie.get('topic');
    if (ctrlInfo.chapterId && topic && isValidTime) {
      setShowError(false);
      setSubject(topic);
      submitForm({
        chapterId: ctrlInfo.chapterId,
        time: time,
      });
    } else {
      setShowError(true);
      toast.info(t('tinfo'));
    }
  };

  const handleChapterChange = (value: string) => {
    setSelectedChapterLocal(value);
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <div>
        <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
          {t('create-controle.chapter.title')}{' '}
          {(ctrlInfo.chapterId.length <= 0 && !showError) ||
          ctrlInfo.chapterId.length > 0 ? (
            <InfoTooltip message="Choisir un chapitre" />
          ) : (
            <ErrorTooltip message="Vous devrez choisir un chapitre" />
          )}
        </div>

        <Select onValueChange={handleChapterChange}>
          <SelectTrigger className="w-[400px] max-w-full">
            {selectedChapterLocal ? (
              <SelectValue
                placeholder={t('create-controle.chapter.placeholder')}
              />
            ) : (
              t('create-controle.chapter.placeholder')
            )}
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              {chapters.map((chapter) => (
                <SelectItem key={chapter.id} value={chapter.id}>
                  {getLangProps({
                    base: 'title',
                    obj: chapter,
                    lang: locale,
                  })}
                </SelectItem>
              ))}
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>

      <div className="w-[400px] flex items-center justify-between gap-1">
        <p>{t('create-controle.duration')}</p>
        <TimePicker date={time} setDate={(value) => setTime(value!)} />
      </div>

      <div className="w-full flex justify-center items-center mt-6">
        <Button
          className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
          onClick={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            t('create-controle.submit')
          )}
        </Button>
      </div>
    </div>
  );
};

export default CtrlFromDb;
