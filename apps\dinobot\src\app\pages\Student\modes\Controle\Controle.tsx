import React from 'react'
import { useTranslation } from 'react-i18next'
import { getLangDir } from 'rtl-detect'
import Timer from './components/Timer/timer'
import ControlPreview from './components/control-preview'
import AnswerControl from './components/answer-control'

const Controle = () => {
    const { i18n } = useTranslation()
    const dir = getLangDir(i18n.language)

    return (
        <div dir={dir} className="flex size-full flex-col xl:flex-row relative grow">
            <div className="xl:absolute fixed xl:top-5 xl:bottom-auto bottom-2 left-1/2 -translate-x-1/2  z-20">
                <Timer />
            </div>
            <div className=" xl:w-1/2 w-full border-l-gray-700 border-2 relative">
                <ControlPreview />
            </div>
            <div className=" xl:w-1/2 w-full border-r-gray-700 border-2">
                <AnswerControl />
            </div>
        </div>
    )
}

export default Controle
