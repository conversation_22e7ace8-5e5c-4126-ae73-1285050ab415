import React from 'react'
import { useTranslation } from 'react-i18next'
import { useForm } from 'react-hook-form'
import { JoinClassData } from '../my-courses.types'
import useMyCourses from '../hooks/useMyCourses'
import { Button, Input } from '@dinobot/components-ui'

interface MyCoursesFormProps {
  onCancel?: () => void
  onSuccess?: () => void
}

function MyCoursesForm({ onCancel, onSuccess }: MyCoursesFormProps) {
  const { t } = useTranslation(['app/courses/index'])
  
  const { joinClass, isJoining, error } = useMyCourses()

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<JoinClassData>()

  const onSubmit = async (data: JoinClassData) => {
    joinClass(data, {
      onSuccess: () => {
        reset()
        onSuccess?.()
      }
    })
  }

  const handleCancel = () => {
    reset()
    onCancel?.()
  }

  return (
    <form className="flex flex-col gap-2" onSubmit={handleSubmit(onSubmit)}>
      <Input
        onKeyDown={e => {
          if (e.key === 'Enter') {
            e.preventDefault()
            handleSubmit(onSubmit)()
          }
        }}
        placeholder="XXX-XXXX-XXX"
        {...register('code', {
          minLength: {
            value: 10,
            message: t('error.length')
          },
          required: {
            value: true,
            message: t('error.required')
          }
        })}
      />
      {errors.code?.message && (
        <span className="text-red-500">
          {errors.code.message.toString()}
        </span>
      )}
      {error && (
        <span className="text-red-500">
          {error}
        </span>
      )}
      <div
        className="w-full h-fit flex mt-12"
        style={{ justifyContent: 'space-between' }}
      >
        <Button
          type="button"
          variant="link"
          className="text-dinoBotLightGray underline"
          onClick={handleCancel}
        >
          {t('dialog.cancel')}
        </Button>
        <Button
          variant="link"
          className="text-dinoBotBlue underline"
          type="submit"
          disabled={isJoining}
        >
          {t('dialog.submit')}
        </Button>
      </div>
    </form>
  )
}

export default MyCoursesForm
