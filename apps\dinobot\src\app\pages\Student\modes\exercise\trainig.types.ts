import { z } from 'zod'
import {questionsTypeSchema} from '@dinobot/prisma'
import { DesmosSchema } from '@dinobot/utils'
import * as Prisma from '@prisma/client'

const QuestionSchema = z.object({
    knowledge: z
        .string()
        .describe(
            "The title of the question. Provide concise context about the question's topic. Must not be long."
        ),
    question: z
        .string()
        .describe(
            'If the question is INDEPENDENT, ensure it is different each time. If RELIANT, respect the main statement and do not repeat it here.'
        ),
    solution: z
        .string()
        .describe(
            'The solution to the question. This field is required; always provide a solution.'
        ),
    statement: z
        .string()
        .optional()
        .describe(
            'The statement of the question, required, make sure to always provide it.'
        )
})

export const GeneratedQuestionSchema = QuestionSchema.extend({
    desmosCode: DesmosSchema.nullable()
        .optional()
        .describe(
            'The desmos code for the question, make sure to respect the user language, should be null if exercise is not graphical'
        ),
    solution: z
        .string()
        .describe(
            'The solution of the question, required, make sure to always provide it.'
        )
})

// Schéma de base pour les questions générées
export const GeneratedQuestionsSchema = z.object({
    output: z.array(QuestionSchema)
})

export const GeneratedQuestionWithSolutionsSchema = z.object({
    output: z.array(GeneratedQuestionSchema),
    statement: z
        .string()
        .describe(
            'Consider the provided statement as the input. If there are no previous questions, create a statement for new inquiries.'
        )
})

export const GenerateQuestionsSchema = QuestionSchema.extend({
    questionNbr: z.number().describe('The number of questions to generate'),
    exerciseType: questionsTypeSchema
        .optional()
        .describe('type of the questions to generate reliant or independant'),
    previousQuestions: z
        .array(GeneratedQuestionSchema)
        .optional()
        .describe('The previous questions')
})
const GenerateQuestionsTypeId = GenerateQuestionsSchema.extend({
    id: z.number()
})

export type GenerateQuestionsType = z.infer<typeof GenerateQuestionsTypeId>

export const QuestionSearchSchema = z.object({
    partId: z.string(),
    chapterName: z.string().optional(),
    partName: z.string().optional(),
    levelName: z.string().optional(),
    domainName: z.string().optional(),
    exerciseType: questionsTypeSchema.optional()
})
// Custom types (unchanged)
export type QuestionContentType =
    | 'text'
    | 'html'
    | 'latex'
    | 'image'
    | 'audio'
    | 'video'
interface User {
    id: string
    firstName: string
    lastName: string
}
export interface Domain {
    id: number
    name: string
}

export interface Level {
    id: number
    name: string
}

export interface Chapter {
    id: string
    title: string
    title_en?: string | undefined | null
    title_ar?: string | undefined | null
    domainId: number
    levelId: number
    createdAt: Date
    updatedAt: Date | undefined

    domain: Domain | undefined
    level: Level | undefined

    disabled: boolean
}

export interface Part {
    id: string
    name: string
    name_en?: string | undefined | null
    name_ar?: string | undefined | null
    chapterId: string
    createdAt: Date
    updatedAt: Date | undefined
    chapter: Chapter | undefined
}

export interface Question {
    id: string
    name: string | undefined
    knowledge: string | undefined
    content: string
    contentType: QuestionContentType
    solution: string
    difficulty: number
    partId: string
    createdAt: Date
    updatedAt: Date | undefined
    part: Part | undefined
    author: User | undefined
    source: string
}

type Exam = Partial<Prisma.Exam>
type Exercise = Partial<Prisma.Exercise>

export interface ExoOutput extends Exercise {
    exam: Exam
    subjects: { subject: { name: string } }[]
}

export interface ExamDomain {
    id: string
    name: string
}

export interface ExoSubject {
    id: string
    name: string
}

export interface ExoSortsAndFilters {
    sort: ExoSorting
    filters: ExoFilters
    pagination: ExoPagination
}
export interface ExoSorting {
    column: SortColumn
    order: 'asc' | 'desc' | undefined
}
export type SortColumn =
    | 'exo.title'
    | 'exam.title'
    | 'exam.year'
    | 'exam.type'
    | 'exo.category'
    | 'none'

export interface ExoFilters {
    exoTitle?: string | undefined
    internalTitle?: string | undefined
    examtitle?: string | undefined
    examYear?: { from: number | undefined; to: number | undefined }
    examType?: Prisma.ExamType | undefined
    domainId?: number | undefined
    levelId?: number | undefined
    domainName?: string | undefined
    exoCategory?: string[]
}
export interface ExoPagination {
    skip?: number | undefined
    take?: number | undefined
}


export interface TrainingModeGeneratorInput {
    partId?: string
    partIds?: string[]
    questionTitle?: string
    exoId?: string
    difficulty: 0 | 1 | 2 | 3
    variation: 'moindre' | 'moyenne' | 'élevée' | 'très élevée'
    numberOfExercises: number
    numberOfQuestions: number
    llmModel?: string
    file?: {
        name?: string
        extension?: string
        data: string
    }
    solutionFile?: {
        name?: string
        extension?: string
        data: string
    }
    customPrompt?: string
    lastResult?: string
    chapterIds?: string[]
    domain?: Domain
    level?: Level
}
export interface Exo {
    id?: string | undefined
    title?: string | undefined
    assignment?: string | undefined
    assignmentMedia?: string | undefined
    solutionMedia?: string | undefined
}