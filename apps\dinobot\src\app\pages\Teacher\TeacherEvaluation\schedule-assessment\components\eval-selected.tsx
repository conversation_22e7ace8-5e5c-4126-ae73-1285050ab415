import { Button } from '@dinobot/components-ui'
import { Eye, Timer } from 'lucide-react'
import moment from 'moment'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useScheduleAssessmentStore } from '../../store/use-schedule-assessment-store'

const EvalSelected = () => {
    const { t } = useTranslation('teacher/myClass/evaluation/schedule/form')
    const selectedControl = useScheduleAssessmentStore(s => s.selectedControl)
    return (
        <div className="border-2 rounded-lg border-dinoBotRedOrange/60 w-56 p-2">
            <div className="flex justify-between items-center">
                <div>
                    <h3 className="text-dinoBotRedOrange flex gap-1">
                        <Timer />
                        {selectedControl?.name}
                    </h3>
                    <p className="text-center text-dinoBotGray text-sm">
                        {t('create_at')}{' '}
                        {moment(selectedControl?.createdAt).format('DD/mm/YY')}
                    </p>
                </div>
                <div className="flex gap-2 -mt-4">
                    <Button variant="ghost" className="p-0">
                        <Eye />
                    </Button>
                </div>
            </div>
            <p>{selectedControl?.description}</p>
        </div>
    )
}

export default EvalSelected
