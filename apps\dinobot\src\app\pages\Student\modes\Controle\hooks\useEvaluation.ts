import { useQuery, useMutation } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext';
import { PlannedEvaluationWithPartialRelations } from '@dinobot/prisma'

export const usePlannedEvaluation = (evaluationId: string | undefined) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['planned-evaluation', evaluationId],
        queryFn: async () => {
            if (!evaluationId) throw new Error('Evaluation ID is required')
            const response = await apiClient.get(`/api/control-mode/planned/${evaluationId}`)
            return response.data as PlannedEvaluationWithPartialRelations
        },
        enabled: !!evaluationId && !!apiClient
    })
}

export const useSignedControlMedia = () => {
    const apiClient = useAuthApiClient()

    return {
        getSignedUrl: async (fileUrl: string, expiresIn: number) => {
            const response = await apiClient.get(`/api/control-mode/media/signed`, {
                params: {
                    url: fileUrl,
                    expiresIn: expiresIn
                }
            })
            return response.data.signedUrl
        }
    }
}

export const useStudentSubmissions = (controlId: string | undefined) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['student-submissions', controlId],
        queryFn: async () => {
            if (!controlId) return null
            const response = await apiClient.get(`/api/control-mode/student-submissions/${controlId}`)
            return response.data
        },
        enabled: !!controlId && !!apiClient
    })
}

export const useCreateStudentSubmission = () => {
    const apiClient = useAuthApiClient()

    return useMutation({
        mutationFn: async (submissionData: any) => {
            const response = await apiClient.post('/api/control-mode/student-submissions', submissionData)
            return response.data
        }
    })
}
