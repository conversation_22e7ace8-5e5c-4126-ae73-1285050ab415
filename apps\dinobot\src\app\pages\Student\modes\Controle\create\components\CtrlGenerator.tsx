import React from 'react';
import { useTranslation } from 'react-i18next';
import { Chapter, Part } from '../CreateControle.types';
import CtrlFromDb from './CtrlFromDb';

interface CtrlGeneratorProps {
  chapters: Chapter[];
  getParts: (chapterId: string) => void;
  submitForm: (formData: { chapterId: string; time: Date }) => void;
  isSubmitting: boolean;
}

const CtrlGenerator: React.FC<CtrlGeneratorProps> = ({
  chapters,
  getParts,
  submitForm,
  isSubmitting,
}) => {
  const { t } = useTranslation(['app/mode']);

  return (
    <div className="w-full h-auto relative flex flex-col gap-12">
      <h2 className="text-2xl md:text-3xl text-dinoBotVibrantBlue leading-9 font-extrabold text-center underline mb-5">
        {t('create-controle.title')}
      </h2>

      <div className="py-8 pt-11 w-[350px] sm:w-[500px] md:w-[600px] bg-clip-padding backdrop-blur-md backdrop-opacity-90 bg-white/60 saturate-100 backdrop-contrast-100 border border-dinoBotSky rounded">
        <CtrlFromDb
          chapters={chapters}
          getParts={getParts}
          submitForm={submitForm}
          isSubmitting={isSubmitting}
        />
      </div>
    </div>
  );
};

export default CtrlGenerator;
