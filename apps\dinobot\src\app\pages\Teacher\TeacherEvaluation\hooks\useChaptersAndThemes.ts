import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { Chapter, ThemePartial } from '@dinobot/prisma'

export const useChaptersAndThemes = (domainId?: string, levelId?: string) => {
    const apiClient = useAuthApiClient()

    // Fetch chapters
    const {
        data: chapters,
        isLoading: isLoadingChapters,
        error: chaptersError
    } = useQuery({
        queryKey: ['chapters', domainId, levelId],
        queryFn: async (): Promise<Chapter[]> => {
            if (!domainId || !levelId) return []
            const response = await apiClient.get(`/api/chapters`, {
                params: { domainId, levelId }
            })
            return response.data
        },
        enabled: !!domainId && !!levelId && !!apiClient
    })

    // Fetch themes
    const {
        data: themes,
        isLoading: isLoadingThemes,
        error: themesError
    } = useQuery({
        queryKey: ['themes', domainId, levelId],
        queryFn: async (): Promise<ThemePartial[]> => {
            if (!domainId || !levelId) return []
            const response = await apiClient.get(`/api/themes`, {
                params: { domainId, levelId }
            })
            return response.data
        },
        enabled: !!domainId && !!levelId && !!apiClient
    })

    const isLoading = isLoadingChapters || isLoadingThemes
    const error = chaptersError || themesError

    return {
        // Data
        chapters: chapters || [],
        themes: themes || [],

        // Loading states
        isLoading,

        // Error states
        error: error?.message
    }
}

export default useChaptersAndThemes
