import {
    Tooltip,
    TooltipContent,
    TooltipTrigger
} from '@dinobot/components-ui'
import { Info } from 'lucide-react'
import React from 'react'
import { useTranslation } from 'react-i18next'

/**
 * MediaTooltip Component
 *
 * Displays a tooltip with information about accepted file formats and size limits
 * for media uploads in the evaluation creation process.
 */
function MediaTooltip() {
    // Initialize translations
    const { t } = useTranslation('teacher/myClass/evaluation')

    return (
        <Tooltip delayDuration={100}>
            <TooltipTrigger className="max-w-fit text-dinoBotBlue hover:text-dinoBotBlue">
                <Info />
            </TooltipTrigger>
            <TooltipContent>
                <h3>{t('mediaTooltip.acceptedFormats')}</h3>
                <p>
                    <strong>{t('mediaTooltip.images')}</strong> PNG, JPEG, GIF,
                    SVG
                    <br />
                    <strong>{t('mediaTooltip.videos')}</strong> MP4, WebM
                </p>
                <p>
                    <strong>{t('mediaTooltip.maxSize')}</strong>{' '}
                    {t('mediaTooltip.sizeLimit', { size: 10 })}
                </p>
                <p>{t('mediaTooltip.ensureCompliance')}</p>
            </TooltipContent>
        </Tooltip>
    )
}

export default MediaTooltip
