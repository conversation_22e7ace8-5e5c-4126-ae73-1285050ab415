export interface Chapter {
  id: string
  title: string
  title_en?: string | null
  title_ar?: string | null
  domain?: Domain
  level?: Level
}

export interface Part {
  id: string
  name: string
  name_en?: string | null
  name_ar?: string | null
  chapterId: string
  chapter?: Chapter
}

export interface Domain {
  id: number
  name: string
  name_en?: string | null
  name_ar?: string | null
  color?: string | null
  createdAt: Date
  updatedAt: Date
  levelTypeId: number
  disabled: boolean
}

export interface Level {
  id: number
  name: string
  name_en?: string | null
  name_ar?: string | null
}

export interface CtrlInfo {
  chapterId: string
  partId: string
}

export interface CreateControleFormData {
  chapterId: string
  time: Date
}

export interface FeatureFlag {
  name: string
  enabled: boolean
  route?: string
}

export interface CreateControleState {
  subject: string | null
  ctrlInfo: CtrlInfo
  time: Date
  chapters: Chapter[]
  parts: Part[]
  isLoading: boolean
  error: string | null
  selectedChapter: string | null
  showError: boolean
}

export interface CreateControleActions {
  setSubject: (subject: string | null) => void
  updateCtrlInfo: (field: keyof CtrlInfo, value: string) => void
  setTime: (time: Date) => void
  setChapters: (chapters: Chapter[]) => void
  setParts: (parts: Part[]) => void
  setIsLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setSelectedChapter: (chapter: string | null) => void
  setShowError: (show: boolean) => void
  reset: () => void
}
