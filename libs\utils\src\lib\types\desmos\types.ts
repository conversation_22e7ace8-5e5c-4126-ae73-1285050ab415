import { z } from 'zod'

const ListSchema = z.object({
    type: z.literal('expression').catch('expression'),
    id: z.string().optional(),
    color: z.string().optional(),
    latex: z.string().optional(),
    lineStyle: z
        .enum(['POINT', 'OPEN', 'CROSS', 'SOLID', 'DASHED', 'DOTTED'])
        .optional(),
    label: z.string().optional(),
    showLabel: z.boolean().optional(),
    dragMode: z.enum(['NONE']).catch('NONE'),
    pointStyle: z
        .enum(['POINT', 'OPEN', 'CROSS', 'SOLID', 'DASHED', 'DOTTED'])
        .optional(),
    hidden: z.boolean().optional(),
    fillOpacity: z
        .number()
        .optional()
        .describe("Contrôle l'opacité des formes remplies (0 à 1)"),
    lineOpacity: z
        .number()
        .optional()
        .describe("Contrôle l'opacité des lignes (0 à 1)"),
    lineWidth: z.number().optional().describe('Largeur des lignes en pixels'),
    labelSize: z
        .enum(['SMALL', 'MEDIUM', 'LARGE'])
        .optional()
        .describe("Taille du texte de l'étiquette"),
    labelOrientation: z
        .enum(['ABOVE', 'BELOW', 'LEFT', 'RIGHT'])
        .optional()
        .describe(
            "Position de l'étiquette par rapport au point ou à l'expression"
        ),
    filled: z
        .boolean()
        .optional()
        .describe(
            'Indique si les polygones et les régions doivent être remplis'
        ),
    clickableInfo: z
        .string()
        .optional()
        .describe("Information affichée lors du clic sur l'expression"),
    secret: z
        .boolean()
        .optional()
        .describe(
            "Indique si l'expression doit être masquée dans la liste des expressions"
        )
})
export type List = z.infer<typeof ListSchema>

export const ViewportSchema = z.object({
    xmin: z.number(),
    ymin: z.number(),
    xmax: z.number(),
    ymax: z.number()
})
export type Viewport = z.infer<typeof ViewportSchema>

export const ExpressionsSchema = z.object({
    list: z.array(ListSchema)
})
export type Expressions = z.infer<typeof ExpressionsSchema>

export const GraphSchema = z.object({
    viewport: ViewportSchema.optional().describe(
        'Définit la région visible du graphique avec les coordonnées xmin, ymin, xmax, ymax'
    ),
    grid: z
        .boolean()
        .optional()
        .describe(
            'Contrôle si les lignes de la grille sont visibles (true) ou masquées (false)'
        ),
    xAxisMinorSubdivisions: z
        .number()
        .optional()
        .describe(
            "Nombre de graduations mineures entre les lignes principales de la grille sur l'axe x"
        ),
    yAxisMinorSubdivisions: z
        .number()
        .optional()
        .describe(
            "Nombre de graduations mineures entre les lignes principales de la grille sur l'axe y"
        ),
    xAxisLabel: z
        .string()
        .optional()
        .describe(
            "Étiquette textuelle pour l'axe x (ex: 'x', 'temps', 'distance')"
        ),
    yAxisLabel: z
        .string()
        .optional()
        .describe(
            "Étiquette textuelle pour l'axe y (ex: 'y', 'vitesse', 'hauteur')"
        ),
    xAxisArrowMode: z
        .string()
        .optional()
        .describe(
            "Style de flèche pour l'axe x. Options: 'NONE', 'POSITIVE' (→), 'NEGATIVE' (←), ou 'BOTH' (↔)"
        ),
    yAxisArrowMode: z
        .string()
        .optional()
        .describe(
            "Style de flèche pour l'axe y. Options: 'NONE', 'POSITIVE' (↑), 'NEGATIVE' (↓), ou 'BOTH' (↕)"
        ),
    showXAxis: z
        .boolean()
        .optional()
        .describe(
            "Contrôle si la ligne de l'axe x est visible (true) ou masquée (false)"
        ),
    showYAxis: z
        .boolean()
        .optional()
        .describe(
            "Contrôle si la ligne de l'axe y est visible (true) ou masquée (false)"
        ),
    squareAxes: z
        .boolean()
        .optional()
        .describe(
            'Force le graphique à maintenir une échelle égale sur les deux axes'
        ),
    polarMode: z
        .boolean()
        .optional()
        .describe(
            'Bascule entre les systèmes de coordonnées cartésiennes et polaires'
        ),
    backgroundColor: z
        .string()
        .optional()
        .describe("Couleur d'arrière-plan du graphique"),
    enableZoom: z.boolean().optional().describe('Permet ou empêche le zoom'),
    enablePan: z
        .boolean()
        .optional()
        .describe('Permet ou empêche le déplacement'),
    showGrid: z
        .boolean()
        .optional()
        .describe("Affiche ou masque la grille d'arrière-plan"),
    showAxesNumbers: z
        .boolean()
        .optional()
        .describe('Affiche ou masque les nombres sur les axes')
})
export type Graph = z.infer<typeof GraphSchema>

export const DesmosSchema = z.object({
    expressions: ExpressionsSchema.optional(),
    graph: GraphSchema.optional().describe('Graph settings')
})

export const llmOutputRecipe = z.object({
    output: z.array(
        z.object({
            questionContent: z
                .string()
                .describe('The question content of the exercise'),
            desmosCode: DesmosSchema.optional()
                .nullable()
                .describe(
                    'The desmos code for the question, make sure to respect the user language, should be null if exercise is not graphical'
                ),
            contentType: z.enum(['text', 'html', 'latex']).default('latex')
        })
    )
})

export type DesmosType = z.infer<typeof DesmosSchema>
