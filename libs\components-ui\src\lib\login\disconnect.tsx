'use client'

import { LogOut } from 'lucide-react'
import Cookies from 'js-cookie'
import {useExamsStore} from '@dinobot/stores'
import { Button } from '../ui/button'
import { useTranslation } from 'react-i18next'

type DisconnectButtonProps = {
    color?: string
}

export default function DisconnectButton({ color }: DisconnectButtonProps) {
    const { setExercise } = useExamsStore()
    const { t } = useTranslation(['app/headers'],{keyPrefix:'disconnect'})

    return (
        <Button
            onClick={() => {
                setExercise(null)
                Cookies.set('feature', 'Chat')
            }}
            title="Se déconnecter"
            variant="ghost"
            className={`sm:-ml-2 sm:mr-2 size-9 p-0 lg:flex ${color ? color : 'text-dinoBotBlue border-dinoBotBlue hover:bg-dinoBotBlue hover:text-white hover:border-dinoBotBlue transition-all duration-300'}`}
        >
            <LogOut />
            <span className="sr-only">{t('title')}</span>
        </Button>
    )
}
