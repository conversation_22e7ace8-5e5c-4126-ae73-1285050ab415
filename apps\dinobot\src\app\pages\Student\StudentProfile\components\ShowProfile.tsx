import React from 'react'
import { useTranslation } from 'react-i18next'
import { useStudentProfileStore } from '../store/StudentProfile.store'

export default function ShowProfile() {
    const { t, i18n } = useTranslation(['app/chat'], { keyPrefix: 'profile.profil' })
    const { user } = useStudentProfileStore()

    const lang = i18n.language 

    const getTranslatedGender = (key: string) => {
        return key ? t(`genders.${key}`) : t('nodefine')
    }

    return (
        <div className="flex flex-col">
            <div className="flex flex-col md:grid md:grid-cols-2 md:gap-4 mb-4">
                <div className="flex flex-col mb-2">
                    <div className="flex font-bold text-base">{t('nom')}</div>
                    <div className="flex animate-fade-in">
                        {user.lastName || t('nodefine')}
                    </div>
                </div>
                <div className="flex flex-col mb-2">
                    <div className="flex font-bold text-base">
                        {t('prenom')}
                    </div>
                    <div className="flex animate-fade-in">
                        {user.firstName || t('nodefine')}
                    </div>
                </div>
            </div>
            <div className="flex flex-col md:grid md:grid-cols-2 md:gap-4 ">
                <div className="flex flex-col mb-2">
                    <div className="flex font-bold text-base">{t('genre')}</div>
                    <div className="flex animate-fade-in">
                        {getTranslatedGender(user.gender!)}
                    </div>
                </div>
                <div className="flex flex-col mb-2">
                    <div className="flex font-bold text-base">
                        {t('niveau')}
                    </div>
                    <div className="flex animate-fade-in">
                        {lang === 'fr'
                            ? user.userLevel?.name
                            : lang === 'en'
                              ? user.userLevel?.name_en
                              : user.userLevel?.name_ar}
                    </div>
                </div>
            </div>
            {user.birthDate && (
                <div className="flex flex-col mb-2">
                    <div className="flex font-bold text-base">
                        {t('birth-date')}
                    </div>
                    <div className="flex animate-fade-in">
                        {new Date(user.birthDate).toLocaleDateString(
                            lang === 'ar'
                                ? 'ar-SA'
                                : lang === 'fr'
                                  ? 'fr-FR'
                                  : 'en-US'
                        )}
                    </div>
                </div>
            )}
        </div>
    )
}