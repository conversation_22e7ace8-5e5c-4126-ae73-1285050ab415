import React from 'react'
import { useTranslation } from 'react-i18next'

type SubscriptionStatus =
    | 'trialing'
    | 'active'
    | 'canceled'
    | 'incomplete'
    | 'incomplete_expired'
    | 'past_due'
    | 'unpaid'
    | 'paused'

const SubscriptionStatusDisplay: React.FC<{ status: SubscriptionStatus }> = ({
    status
}) => {
    const { t } = useTranslation(['app/chat'], { keyPrefix: 'profile.paiement' })
    switch (status) {
        case 'trialing':
            return (
                <div className="text-dinoBotGreen font-bold">{t('essai')}</div>
            )
        case 'active':
            return (
                <div className="text-dinoBotGreen font-bold">{t('actif')}</div>
            )
        case 'canceled':
            return (
                <div className="text-dinoBotRed font-bold">{t('annule')}</div>
            )
        case 'incomplete':
            return (
                <div className="text-dinoBotRed font-bold">
                    {t('paiement-incomlet')}
                </div>
            )
        case 'incomplete_expired':
            return (
                <div className="text-dinoBotRed font-bold">{t('expire')}</div>
            )
        case 'past_due':
            return (
                <div className="text-dinoBotRed font-bold">{t('retart')}</div>
            )
        case 'unpaid':
            return (
                <div className="text-dinoBotRed font-bold">{t('impaye')}</div>
            )
        case 'paused':
            return (
                <div className="text-dinoBotRed font-bold">{t('en-pause')}</div>
            )
        default:
            return (
                <div className="text-dinoBotRed font-bold">{t('inactif')}</div>
            )
    }
}

export default SubscriptionStatusDisplay