import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'
import { ChatWithMessages } from '../chat.types'

export const useChat = () => {
    const apiClient = useAuthApiClient()
    const queryClient = useQueryClient()

    // Fetch all chats
    const chatsQuery = useQuery({
        queryKey: ['chats'],
        queryFn: async (): Promise<ChatWithMessages[]> => {
            const response = await apiClient.get('/api/chat/chats')
            return response.data
        }
    })

    // Fetch single chat by id
    const getChatById = async (id: string): Promise<ChatWithMessages | null> => {
        try {
            const response = await apiClient.get(`/api/chat/chats/${id}`)
            return response.data
        } catch (error) {
            console.error('Error fetching chat:', error)
            return null
        }
    }

    // Check if chat exists
    const checkChatExistence = async (path: string): Promise<ChatWithMessages | null> => {
        try {
            const id = path.replace('/chat/', '')
            return await getChatById(id)
        } catch (error) {
            console.error('Error checking chat existence:', error)
            return null
        }
    }

    // Remove chat
    const removeChatMutation = useMutation({
        mutationFn: async (id: string) => {
            const response = await apiClient.delete(`/api/chat/chats/${id}`)
            return response.data
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['chats'] })
        }
    })

    // Clear all chats
    const clearChatsMutation = useMutation({
        mutationFn: async () => {
            const response = await apiClient.delete('/api/chat/chats')
            return response.data
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['chats'] })
        }
    })

    // Update chat title
    const updateChatTitleMutation = useMutation({
        mutationFn: async ({ id, title }: { id: string; title: string }) => {
            const response = await apiClient.put(`/api/chat/chats/${id}/title`, { title })
            return response.data
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['chats'] })
        }
    })

    // Pin/Unpin chat
    const pinChatMutation = useMutation({
        mutationFn: async ({ id, pin }: { id: string; pin: boolean }) => {
            const endpoint = pin 
                ? `/api/chat/chats/${id}/pin`
                : `/api/chat/chats/${id}/pin`
            const method = pin ? 'post' : 'delete'
            const response = await apiClient[method](endpoint)
            return response.data
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['chats'] })
        }
    })

    // Check messages access
    const checkMessagesAccessQuery = useQuery({
        queryKey: ['messages-access'],
        queryFn: async () => {
            const response = await apiClient.get('/api/chat/access/messages')
            return response.data
        }
    })

    // Check files access
    const checkFilesAccessQuery = useQuery({
        queryKey: ['files-access'],
        queryFn: async () => {
            const response = await apiClient.get('/api/chat/access/files')
            return response.data
        }
    })

    // Get file by id
    const getFile = async (id: string) => {
        try {
            const response = await apiClient.get(`/api/chat/files/${id}`)
            return response.data
        } catch (error) {
            console.error('Error fetching file:', error)
            return null
        }
    }

    return {
        // Queries
        chats: chatsQuery.data,
        isLoadingChats: chatsQuery.isLoading,
        chatsError: chatsQuery.error,
        messagesAccess: checkMessagesAccessQuery.data,
        filesAccess: checkFilesAccessQuery.data,

        // Actions
        getChatById,
        checkChatExistence,
        removeChat: removeChatMutation.mutate,
        clearChats: clearChatsMutation.mutate,
        updateChatTitle: updateChatTitleMutation.mutate,
        pinChat: (id: string) => pinChatMutation.mutate({ id, pin: true }),
        unpinChat: (id: string) => pinChatMutation.mutate({ id, pin: false }),
        getFile,

        // Loading states
        isRemovingChat: removeChatMutation.isPending,
        isClearingChats: clearChatsMutation.isPending,
        isUpdatingTitle: updateChatTitleMutation.isPending,
        isPinning: pinChatMutation.isPending
    }
}
 