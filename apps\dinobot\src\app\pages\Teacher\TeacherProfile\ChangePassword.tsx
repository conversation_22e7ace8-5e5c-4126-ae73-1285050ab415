import React from 'react';
import { useTranslation } from 'react-i18next';
import ChangePasswordForm from './components/ChangePasswordForm';

const ChangePassword: React.FC = () => {
    const { t } = useTranslation(['app/chat']);

    return (
        <div className="w-full flex flex-col justify-start overflow-y-auto">
            <div className="py-8 px-10 flex gap-3 items-center">
                <div className="text-4xl font-bold text-gray-700">
                    {t('profile.change-password.title')}
                </div>
            </div>
            <div className="max-w-full px-10 flex items-center">
                <div className="md:w-[70%] flex flex-col px-10 overflow-y-auto">
                    <div className="flex flex-col">
                        <div className="text-2xl text-blue-600 font-bold mb-10">
                            {t('profile.change-password.title')}
                        </div>
                        <ChangePasswordForm />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ChangePassword;