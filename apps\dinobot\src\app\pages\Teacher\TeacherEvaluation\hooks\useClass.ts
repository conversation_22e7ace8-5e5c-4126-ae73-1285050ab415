import { useQuery } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'

export const useClass = (id: string | undefined) => {
    const apiClient = useAuthApiClient()

    return useQuery({
        queryKey: ['class', id],
        queryFn: async (): Promise<any> => {
            if (!id || !apiClient) return null
            
            const response = await apiClient.get(`/api/classes/${id}`)
            return response.data
        },
        enabled: !!id && !!apiClient
    })
}