import React from 'react'
import LayoutEvaluation from '../components/layout-evaluation'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'

type LayoutProps = {
    children: React.ReactNode
}

const layout = ({ children }: LayoutProps) => {
    const { classId } = useParams<{ classId: string }>()
    const { t } = useTranslation()

    return (
        <LayoutEvaluation classId={classId!} title={t('teacher.myClass.evaluation.next.titles.create')}>
            {children}
        </LayoutEvaluation>
    )
}

export default layout
