import { useState, useEffect, useRef, RefObject } from 'react'

const useResizeObserver = <T extends HTMLElement>(
    externalRef?: RefObject<T | null>
) => {
    const [size, setSize] = useState({ width: 0, height: 0 })
    const elRef = useRef<T>(null)
    const elementRef = externalRef || elRef

    useEffect(() => {
        const resizeObserver = new ResizeObserver(entries => {
            for (const entry of entries) {
                const { width, height } = entry.contentRect
                setSize({ width, height })
            }
        })
        const currentElement = elementRef.current
        if (currentElement) {
            resizeObserver.observe(currentElement)
        }

        return () => {
            if (currentElement) {
                resizeObserver.unobserve(currentElement)
            }
        }
    }, [])

    return [size, elementRef] as const
}

export default useResizeObserver
