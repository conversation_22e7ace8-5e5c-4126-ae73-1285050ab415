import { OctagonAlert } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import React from 'react'

function ImageNotFound() {
    const { t } = useTranslation('app/mode/controle/controlPreview')
    return (
        <div className="flex gap-1.5 items-center flex-col text-dinoBotRed justify-center text-center">
            <OctagonAlert className="size-6 mb-0.5" />
            <p>{t('imageNotFound')}</p>
        </div>
    )
}

export default ImageNotFound
