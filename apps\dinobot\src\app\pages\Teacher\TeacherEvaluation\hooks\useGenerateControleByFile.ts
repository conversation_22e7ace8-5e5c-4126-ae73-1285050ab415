import { useMutation } from '@tanstack/react-query'
import { useAuthApiClient } from '../../../../contexts/AppContext'

type GenerateControleByFileParams = {
    formFiles: FormData
    domainName: string
    levelName: string
    isCourse: boolean
    documentType: 'cours' | 'exercice'
}

type ExoQuestion = {
    content: string
    desmosCode: string | number | boolean | object | null
    solution: string
    description: string
}

export const useGenerateControleByFile = () => {
    const apiClient = useAuthApiClient()

    return useMutation({
        mutationFn: async (params: GenerateControleByFileParams): Promise<ExoQuestion[][]> => {
            const { formFiles, ...otherParams } = params

            // Create a new FormData with both files and parameters
            const formData = new FormData()

            // Copy files from the input FormData
            const file = formFiles.get('file') as File
            const solutionFile = formFiles.get('solutionFile') as File

            if (file) formData.append('file', file)
            if (solutionFile) formData.append('solutionFile', solutionFile)

            // Add other parameters as JSON in a separate field
            formData.append('params', JSON.stringify(otherParams))

            const response = await apiClient.post('/api/control-generator/controle-by-file', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            })

            return response.data
        }
    })
}
