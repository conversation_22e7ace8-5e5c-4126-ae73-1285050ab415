import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuthApiClient } from '../../../../../contexts/AppContext';
import { Chapter, Part, Domain, Level, ExoSubject, Question, ExoOutput, TrainingModeGeneratorInput } from '../trainig.types';

export function useExerciseData() {
  const authApiClient = useAuthApiClient();
  const queryClient = useQueryClient();

  // Get chapters by domain and level
  const useChaptersByDomainAndLevel = (domainId?: number, levelId?: number) => {
    return useQuery({
      queryKey: ['chapters', domainId, levelId],
      queryFn: async (): Promise<Chapter[]> => {
        const response = await authApiClient.get(`/api/training-mode/chapters/${domainId}/${levelId}`);
        return response.data;
      },
      enabled: !!domainId && !!levelId,
      staleTime: 5 * 60 * 1000,
    });
  };

  // Get parts by chapter
  const usePartsByChapter = (chapterId?: string) => {
    return useQuery({
      queryKey: ['parts', chapterId],
      queryFn: async (): Promise<Part[]> => {
        const response = await authApiClient.get(`/api/training-mode/parts/${chapterId}`);
        return response.data;
      },
      enabled: !!chapterId,
      staleTime: 5 * 60 * 1000,
    });
  };

  // Get domains by level (from domain-level API)
  const useDomainsByLevel = (levelId?: number) => {
    return useQuery({
      queryKey: ['domains', levelId],
      queryFn: async (): Promise<Domain[]> => {
        const response = await authApiClient.get(`/api/domain-level/user-level-domains`);
        // Filter by levelId if provided
        if (levelId) {
          return response.data.filter((item: any) => item.levelDomain.level.id === levelId)
            .map((item: any) => item.levelDomain.domain);
        }
        return response.data.map((item: any) => item.levelDomain.domain);
      },
      enabled: !!levelId, // Only fetch when levelId is provided
      staleTime: 10 * 60 * 1000,
    });
  };

  // Get levels (from domain-level API)
  const useLevels = () => {
    return useQuery({
      queryKey: ['levels'],
      queryFn: async (): Promise<Level[]> => {
        const response = await authApiClient.get(`/api/domain-level/user-level-domains`);
        // Extract unique levels
        const levels = response.data.map((item: any) => item.levelDomain.level);
        return levels.filter((level: Level, index: number, arr: Level[]) => 
          arr.findIndex(l => l.id === level.id) === index
        );
      },
      staleTime: 10 * 60 * 1000,
    });
  };

  // Get subjects by domain and level
  const useSubjectsByDomainAndLevel = (domainId?: number) => {
    return useQuery({
      queryKey: ['subjects', domainId],
      queryFn: async (): Promise<ExoSubject[]> => {
        const response = await authApiClient.get(`/api/exams/subjects/domain/${domainId}`);
        return response.data;
      },
      enabled: !!domainId ,
      staleTime: 5 * 60 * 1000,
    });
  };

  // Get exams by domain, level and subjects
  const useExamsByDomainLevelSubjects = (domainId?: number, levelId?: number, subjects?: string[]) => {
    return useQuery({
      queryKey: ['exams', domainId, levelId, subjects],
      queryFn: async (): Promise<ExoOutput[]> => {
        const params = new URLSearchParams();
        if (domainId) params.append('domainId', domainId.toString());
        if (levelId) params.append('levelId', levelId.toString());
        if (subjects && subjects.length > 0) {
          subjects.forEach(subject => params.append('subjects', subject));
        }
        
        const response = await authApiClient.get(`/api/exams?${params.toString()}`);
        return response.data;
      },
      enabled: !!domainId && !!levelId,
      staleTime: 5 * 60 * 1000,
    });
  };

  // Get exercise by ID
  const useExerciseById = (exoId?: string) => {
    return useQuery({
      queryKey: ['exercise', exoId],
      queryFn: async () => {
        const response = await authApiClient.get(`/api/training-mode/questions/${exoId}`);
        return response.data;
      },
      enabled: !!exoId,
      staleTime: 5 * 60 * 1000,
    });
  };

  // Search similar questions
  const searchSimilarQuestions = useMutation({
    mutationFn: async (partId: string): Promise<Question[]> => {
      const response = await authApiClient.post('/api/training-mode/questions/search', {
        partId
      });
      return response.data;
    },
  });

  // Generate questions
  const generateQuestions = useMutation({
    mutationFn: async (input: any) => {
      const response = await authApiClient.post('/api/training-mode/questions/generate', input);
      return response.data;
    },
  });

  // Generate exercises
  const generateExercises = useMutation({
    mutationFn: async (input: TrainingModeGeneratorInput) => {
      const response = await authApiClient.post('/api/training-mode/exercises/generate', input);
      return response.data;
    },
  });

  // Regenerate exercises
  const regenerateExercises = useMutation({
    mutationFn: async (input: TrainingModeGeneratorInput) => {
      const response = await authApiClient.post('/api/training-mode/exercises/regenerate', input);
      return response.data;
    },
  });

  return {
    // Query hooks
    useChaptersByDomainAndLevel,
    usePartsByChapter,
    useDomainsByLevel,
    useLevels,
    useSubjectsByDomainAndLevel,
    useExamsByDomainLevelSubjects,
    useExerciseById,

    // Mutations
    searchSimilarQuestions,
    generateQuestions,
    generateExercises,
    regenerateExercises,

    // Loading states
    isSearchingQuestions: searchSimilarQuestions.isPending,
    isGeneratingQuestions: generateQuestions.isPending,
    isGeneratingExercises: generateExercises.isPending,
    isRegeneratingExercises: regenerateExercises.isPending,

    // Error states
    searchQuestionsError: searchSimilarQuestions.error,
    generateQuestionsError: generateQuestions.error,
    generateExercisesError: generateExercises.error,
    regenerateExercisesError: regenerateExercises.error,
  };
}